{"dok_assessment_intro_title": "智能评估设计，专业教学成果", "dok_assessment_intro_enhanced_description": "一款AI驱动的教育评估生成器，只需一键点击即可从任何教学主题创建基于DOK理论的综合性评估。", "dok_assessment_feature_1_title": "一键生成", "dok_assessment_feature_1_text": "输入任何教学主题，即可基于DOK框架瞬间生成完整的多层次评估。", "dok_assessment_feature_2_title": "DOK理论集成", "dok_assessment_feature_2_text": "自动创建涵盖四个认知层次的评估：回忆、技能、策略思维和拓展思维。", "dok_assessment_feature_3_title": "专业品质", "dok_assessment_feature_3_text": "生成具有合理难度递进、清晰评分标准和实施指南的评估题目。", "why_dok_assessment_description_title": "什么是基于DOK的评估？", "why_dok_assessment_description": "基于DOK的评估使用韦伯深度知识框架创建综合性评价，测量不同层次的认知复杂性，确保全面的学习评估。", "dok_assessment_benefits_title": "AI评估生成的优势", "dok_assessment_benefit_1": "时间效率：将数小时的评估设计工作缩短为几分钟。", "dok_assessment_benefit_2": "专业品质：确保教育最佳实践和合理的认知递进。", "dok_assessment_benefit_3": "全面覆盖：自动识别和评估所有关键学习要点。", "dok_assessment_benefit_4": "标准化质量：在所有主题中保持一致的评估标准。", "dok_assessment_uses_title": "完美适用于", "dok_assessment_use_1": "课堂教学：快速生成单元测试和形成性评估。", "dok_assessment_use_2": "课程设计：为新课程创建评估框架。", "dok_assessment_use_3": "质量保证：确保全面覆盖和认知平衡。", "dok_assessment_use_4": "个性化学习：为不同学习需求调整评估。", "dok_assessment_advantages_title": "为什么选择FunBlocks AI评估？", "dok_assessment_advantage_1_title": "教育理论基础", "dok_assessment_advantage_1_text": "基于韦伯DOK框架构建，确保科学支撑的评估设计。", "dok_assessment_advantage_2_title": "智能内容分析", "dok_assessment_advantage_2_text": "AI自动识别3-5个核心知识点并创建平衡的评估。", "dok_assessment_advantage_3_title": "完整实施指南", "dok_assessment_advantage_3_text": "提供详细的评分标准、评分指南和实施建议。", "dok_assessment_advantage_4_title": "灵活可适应", "dok_assessment_advantage_4_text": "适用于任何学科领域，可针对不同年级水平进行定制。", "dok_assessment_target_audience_title": "最大受益群体", "dok_assessment_audience_1_title": "教师和教育工作者", "dok_assessment_audience_1_text": "在确保专业质量和全面覆盖的同时，节省评估设计的时间。", "dok_assessment_audience_2_title": "教学设计师", "dok_assessment_audience_2_text": "创建与学习目标和教育标准相符的评估框架。", "dok_assessment_audience_3_title": "学校管理者", "dok_assessment_audience_3_text": "在各部门间标准化评估质量，确保一致的评价实践。", "dok_assessment_audience_4_title": "教育顾问", "dok_assessment_audience_4_text": "为客户提供基于研究的评估解决方案和专业评价工具。", "dok_assessment_how_it_works_title": "工作原理", "dok_assessment_step_1_title": "输入主题", "dok_assessment_step_1_text": "简单输入任何教学主题、课程内容或学习目标。", "dok_assessment_step_2_title": "AI分析", "dok_assessment_step_2_text": "我们的AI识别关键概念、学习要点和最优DOK分布。", "dok_assessment_step_3_title": "生成评估", "dok_assessment_step_3_text": "获得包含题目、评分标准和实施指南的完整评估。", "faq": "常见问题", "dok_assessment_faq_1_q": "FunBlocks AI评估如何确保教育质量？", "dok_assessment_faq_1_a": "我们的AI基于教育最佳实践和韦伯DOK框架进行训练。它自动确保合理的认知递进、全面覆盖和适龄语言，同时保持专业评估标准。", "dok_assessment_faq_2_q": "它支持哪些学科和年级水平？", "dok_assessment_faq_2_a": "FunBlocks AI评估适用于任何学科领域，可适应不同年级水平。AI会根据您指定的内容和目标受众调整题目复杂性、词汇和评估格式。", "dok_assessment_faq_3_q": "这与传统评估创建工具有何不同？", "dok_assessment_faq_3_a": "与需要手动创建题目的传统工具不同，我们的AI基于教育理论自动生成综合性评估。它节省数小时工作时间，同时确保专业质量和合理的认知平衡。", "dok_assessment_faq_4_q": "我可以自定义生成的评估吗？", "dok_assessment_faq_4_a": "当然可以！虽然AI生成初始评估结构，但您可以完全控制修改题目、调整难度水平和自定义评分标准，以匹配您的具体教学需求和标准。", "dok_assessment_faq_5_q": "基于DOK的评估比常规测试有什么优势？", "dok_assessment_faq_5_a": "基于DOK的评估确保学生在所有认知层次上得到评价，从基本回忆到复杂推理。这提供了学生理解的更完整图景，有助于识别具体的改进领域。", "dok_assessment_faq_6_q": "使用FunBlocks AI评估需要付费吗？", "dok_assessment_faq_6_a": "FunBlocks AI评估为所有用户提供免费使用。新用户可以享受免费试用，所有用户只需登录即可每天获得10次免费AI请求。", "dok_assessment_faq_7_q": "它与使用ChatGPT创建评估相比如何？", "dok_assessment_faq_7_a": "虽然ChatGPT可以生成题目，但FunBlocks AI评估专门为教育评估设计，内置DOK框架、自动内容分析、合理难度递进和完整实施指南——这些功能在通用AI工具中不可用。", "dok_assessment_faq_8_q": "它能帮助创建形成性和总结性评估吗？", "dok_assessment_faq_8_a": "是的！该工具可以生成用于持续学习评价的形成性评估和用于最终评价的总结性评估。您可以指定评估类型和目的以获得最佳结果。"}