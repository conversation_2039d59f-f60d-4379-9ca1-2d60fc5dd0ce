{"dok_assessment_intro_title": "Smart Assessment Design, Professional Results", "dok_assessment_intro_enhanced_description": "An AI-powered educational assessment generator that creates comprehensive, DOK-based assessments from any teaching topic with just one click.", "dok_assessment_feature_1_title": "One-Click Generation", "dok_assessment_feature_1_text": "Input any teaching topic and instantly generate complete multi-level assessments based on DOK framework.", "dok_assessment_feature_2_title": "DOK Framework Integration", "dok_assessment_feature_2_text": "Automatically creates assessments covering all four cognitive levels: Recall, Skills, Strategic Thinking, and Extended Thinking.", "dok_assessment_feature_3_title": "Professional Quality", "dok_assessment_feature_3_text": "Generate assessment questions with proper difficulty progression, clear rubrics, and implementation guidelines.", "why_dok_assessment_description_title": "What is DOK-Based Assessment?", "why_dok_assessment_description": "DOK-based assessment uses <PERSON>'s Depth of Knowledge framework to create comprehensive evaluations that measure different levels of cognitive complexity, ensuring thorough learning evaluation.", "dok_assessment_benefits_title": "Benefits of AI Assessment Generation", "dok_assessment_benefit_1": "Time Efficiency: Transform hours of assessment design into minutes.", "dok_assessment_benefit_2": "Professional Quality: Ensure educational best practices and proper cognitive progression.", "dok_assessment_benefit_3": "Comprehensive Coverage: Automatically identify and assess all key learning points.", "dok_assessment_benefit_4": "Standardized Quality: Maintain consistent assessment standards across all topics.", "dok_assessment_uses_title": "Perfect For", "dok_assessment_use_1": "Classroom Teaching: Generate unit tests and formative assessments quickly.", "dok_assessment_use_2": "Curriculum Design: Create assessment frameworks for new courses.", "dok_assessment_use_3": "Quality Assurance: Ensure comprehensive coverage and cognitive balance.", "dok_assessment_use_4": "Personalized Learning: Adapt assessments for different learning needs.", "dok_assessment_advantages_title": "Why Choose FunBlocks AI Assessment?", "dok_assessment_advantage_1_title": "Educational Theory Foundation", "dok_assessment_advantage_1_text": "Built on Webb's DOK framework, ensuring scientifically-backed assessment design.", "dok_assessment_advantage_2_title": "Intelligent Content Analysis", "dok_assessment_advantage_2_text": "AI automatically identifies 3-5 core knowledge points and creates balanced assessments.", "dok_assessment_advantage_3_title": "Complete Implementation Guide", "dok_assessment_advantage_3_text": "Provides detailed rubrics, scoring guidelines, and implementation suggestions.", "dok_assessment_advantage_4_title": "Flexible and Adaptable", "dok_assessment_advantage_4_text": "Works with any subject area and can be customized for different grade levels.", "dok_assessment_target_audience_title": "Who Benefits Most", "dok_assessment_audience_1_title": "Teachers & Educators", "dok_assessment_audience_1_text": "Save hours on assessment design while ensuring professional quality and comprehensive coverage.", "dok_assessment_audience_2_title": "Instructional Designers", "dok_assessment_audience_2_text": "Create assessment frameworks that align with learning objectives and educational standards.", "dok_assessment_audience_3_title": "School Administrators", "dok_assessment_audience_3_text": "Standardize assessment quality across departments and ensure consistent evaluation practices.", "dok_assessment_audience_4_title": "Educational Consultants", "dok_assessment_audience_4_text": "Provide clients with research-backed assessment solutions and professional evaluation tools.", "dok_assessment_how_it_works_title": "How It Works", "dok_assessment_step_1_title": "Input Your Topic", "dok_assessment_step_1_text": "Simply enter any teaching topic, course content, or learning objective.", "dok_assessment_step_2_title": "AI Analysis", "dok_assessment_step_2_text": "Our AI identifies key concepts, learning points, and optimal DOK distribution.", "dok_assessment_step_3_title": "Generate Assessment", "dok_assessment_step_3_text": "Receive a complete assessment with questions, rubrics, and implementation guide.", "faq": "Frequently Asked Questions", "dok_assessment_faq_1_q": "How does FunBlocks AI Assessment ensure educational quality?", "dok_assessment_faq_1_a": "Our AI is trained on educational best practices and <PERSON>'s DOK framework. It automatically ensures proper cognitive progression, comprehensive coverage, and age-appropriate language while maintaining professional assessment standards.", "dok_assessment_faq_2_q": "What subjects and grade levels does it support?", "dok_assessment_faq_2_a": "FunBlocks AI Assessment works with any subject area and can adapt to different grade levels. The AI adjusts question complexity, vocabulary, and assessment format based on the content and target audience you specify.", "dok_assessment_faq_3_q": "How is this different from traditional assessment creation tools?", "dok_assessment_faq_3_a": "Unlike traditional tools that require manual question creation, our AI automatically generates comprehensive assessments based on educational theory. It saves hours of work while ensuring professional quality and proper cognitive balance.", "dok_assessment_faq_4_q": "Can I customize the generated assessments?", "dok_assessment_faq_4_a": "Absolutely! While AI generates the initial assessment structure, you have complete control to modify questions, adjust difficulty levels, and customize rubrics to match your specific teaching needs and standards.", "dok_assessment_faq_5_q": "What makes DOK-based assessments better than regular tests?", "dok_assessment_faq_5_a": "DOK-based assessments ensure students are evaluated across all cognitive levels, from basic recall to complex reasoning. This provides a more complete picture of student understanding and helps identify specific areas for improvement.", "dok_assessment_faq_6_q": "Is there a cost to use FunBlocks AI Assessment?", "dok_assessment_faq_6_a": "FunBlocks AI Assessment offers free usage for all users. New users can enjoy a free trial, and all users receive 10 free AI requests per day simply by logging in.", "dok_assessment_faq_7_q": "How does it compare to using ChatGPT for assessment creation?", "dok_assessment_faq_7_a": "While ChatGPT can generate questions, FunBlocks AI Assessment is specifically designed for educational assessment with built-in DOK framework, automatic content analysis, proper difficulty progression, and complete implementation guides - features not available in general AI tools.", "dok_assessment_faq_8_q": "Can it help with formative and summative assessments?", "dok_assessment_faq_8_a": "Yes! The tool can generate both formative assessments for ongoing learning evaluation and summative assessments for final evaluation. You can specify the assessment type and purpose for optimal results."}