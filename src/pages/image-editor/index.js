import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Image Editor - Transform Images with Natural Language | FunBlocks AI Tools</title>
        <meta name="description" content="AI Image Editor lets you edit images using simple text instructions. No Photoshop skills needed - just describe what you want to change and our AI will do the rest. Perfect for photo editing, image enhancement, and creative transformations!" />
        <meta name="keywords" content="AI image editor, photo editing AI, natural language image editing, AI photo enhancement, text-to-edit, intelligent image modification, automated photo editing, AI-powered image transformation, smart photo editor, conversational image editing" />
      </Head>
      <MainFrame app={APP_TYPE.imageEditor} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'image-editor'])),
    },
  }
}
