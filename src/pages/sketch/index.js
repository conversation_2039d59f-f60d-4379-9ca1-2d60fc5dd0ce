import Head from 'next/head'
import { Box, Container, Heading } from '@chakra-ui/react'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import getConfig from 'next/config';
import { APP_TYPE } from '../../utils/constants';
import { fetchShowcases } from '../../utils/data';

export default function Home() {
  return (
    <>
      <Head>
        <title>AI Sketch - Transform Your Drawings into Art | FunBlocks AI Tools</title>
        <meta name="description" content="AI Sketch lets you draw simple sketches on a whiteboard and transform them into stunning artwork with AI. Choose from multiple art styles including anime, realistic, cartoon, watercolor, and more. Perfect for artists, designers, and creative enthusiasts!" />
        <meta name="keywords" content="AI sketch, AI drawing, sketch to art, AI artwork generator, digital drawing, AI art styles, creative AI, drawing enhancement, sketch transformation, AI artist, whiteboard drawing, touch drawing, mobile drawing" />
      </Head>
      <MainFrame app={APP_TYPE.sketch} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['sketch', 'common'])),
    },
  }
}
