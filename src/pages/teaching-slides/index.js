import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants'
import getConfig from 'next/config'

export default function Home() {
  const { basePath } = getConfig().publicRuntimeConfig;
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.funblocks.net';
  const canonicalUrl = `${siteUrl}${basePath}/teaching-slides`;

  // Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI EduSlides",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "AI-powered educational slide generation tool designed specifically for educators. Create professional teaching slides with pedagogical frameworks in minutes.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "485"
    },
    "featureList": [
      "AI-powered slide generation",
      "Multiple pedagogical frameworks",
      "Rich multimedia support",
      "Teaching notes and guidance",
      "Interactive elements",
      "Assessment strategies",
      "Professional design principles",
      "Educational effectiveness"
    ],
    "creator": {
      "@type": "Organization",
      "name": "FunBlocks AI",
      "url": "https://www.funblocks.net"
    }
  };

  // FAQ structured data
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How does FunBlocks AI EduSlides enhance teaching preparation?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI EduSlides revolutionizes teaching preparation by automatically generating comprehensive, framework-based teaching slides from any topic. It combines AI technology with proven pedagogical frameworks to create professional-quality educational content in minutes, not hours."
        }
      },
      {
        "@type": "Question",
        "name": "What teaching frameworks are supported?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI EduSlides supports multiple authoritative pedagogical frameworks including Bloom's Taxonomy, Marzano's Strategies, 5E Teaching Model, ADDIE Model, Gagne's Nine Events of Instruction, and Constructivist Learning approaches."
        }
      },
      {
        "@type": "Question",
        "name": "How is it different from regular presentation tools?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Unlike general presentation tools, FunBlocks AI EduSlides is specifically designed for education. It automatically incorporates pedagogical principles, generates teaching notes, suggests interactive activities, and creates assessment strategies."
        }
      },
      {
        "@type": "Question",
        "name": "Can I customize the generated teaching slides?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolutely! While AI generates the initial structure based on educational frameworks, you have complete control over customization. You can modify content, adjust timing, add resources, and tailor the slides to your specific classroom needs."
        }
      },
      {
        "@type": "Question",
        "name": "Is it suitable for different educational levels?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, FunBlocks AI EduSlides adapts to all educational levels from K-12 to higher education and corporate training. The AI automatically adjusts complexity, language, and activities based on the target audience."
        }
      },
      {
        "@type": "Question",
        "name": "Does it support multimedia content?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes! FunBlocks AI EduSlides automatically generates rich multimedia content including tables, mathematical formulas (LaTeX), flowcharts (Mermaid), comparison cards, and various visualization formats."
        }
      },
      {
        "@type": "Question",
        "name": "Is there a fee to use FunBlocks AI EduSlides?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "FunBlocks AI EduSlides offers free usage for all users. New users can enjoy a free trial, and all users have 10 free AI requests per day, simply by logging in."
        }
      }
    ]
  };

  return (
    <>
      <Head>
        <title>FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator | Educational Design Tool</title>
        <meta name="description" content="Transform your teaching with FunBlocks AI EduSlides. Generate professional, framework-based teaching slides in minutes. Support for Bloom's Taxonomy, 5E Model, Marzano's Strategies, and more. Perfect for K-12, higher education, and corporate training." />
        <meta name="keywords" content="AI teaching slides, educational slide generator, teaching slide maker, AI lesson slides, pedagogical frameworks, Bloom's taxonomy slides, 5E model slides, Marzano strategies, educational design tool, teaching preparation, AI courseware, interactive teaching slides, educational technology, instructional design AI, teaching materials generator, classroom slides AI, educational content creation, AI for educators, teaching slide templates, professional teaching slides" />

        {/* Open Graph tags */}
        <meta property="og:title" content="FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator" />
        <meta property="og:description" content="Create professional teaching slides with AI assistance. Support for multiple pedagogical frameworks, rich multimedia content, and comprehensive teaching notes. Perfect for educators at all levels." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_teaching_slides_generated.png`} />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="FunBlocks AI EduSlides: AI-Powered Teaching Slide Generator" />
        <meta name="twitter:description" content="Transform your teaching preparation with AI-generated slides based on proven pedagogical frameworks. Create engaging, professional educational content in minutes." />
        <meta name="twitter:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_teaching_slides_generated.png`} />

        {/* Additional SEO tags */}
        <meta name="robots" content="index, follow" />
        <meta name="author" content="FunBlocks AI" />
        <meta name="publisher" content="FunBlocks AI" />
        <meta name="copyright" content="FunBlocks AI" />
        <meta name="language" content="en" />
        <meta name="revisit-after" content="7 days" />
        <meta name="distribution" content="global" />
        <meta name="rating" content="general" />

        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />

        {/* Schema.org structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />

        {/* FAQ structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
        />
      </Head>
      <MainFrame app={APP_TYPE.teachingSlides} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['teaching-slides', 'common'])),
    },
  }
}
