import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants'
import getConfig from 'next/config'
import { useTranslation } from 'next-i18next'

export default function Home() {
  const { basePath } = getConfig().publicRuntimeConfig;
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.funblocks.net';
  const canonicalUrl = `${siteUrl}${basePath}/dok-assessment`;
  const { t } = useTranslation('dok-assessment');

  // Schema.org structured data for rich results
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "FunBlocks AI Assessment",
    "applicationCategory": "EducationalApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": "AI-powered educational assessment generator based on <PERSON>'s Depth of Knowledge (DOK) framework. Create comprehensive assessments instantly from any teaching topic.",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "312"
    }
  };

  // FAQ structured data for rich results
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": t('dok_assessment_faq_1_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('dok_assessment_faq_1_a')
        }
      },
      {
        "@type": "Question",
        "name": t('dok_assessment_faq_2_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('dok_assessment_faq_2_a')
        }
      },
      {
        "@type": "Question",
        "name": t('dok_assessment_faq_3_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('dok_assessment_faq_3_a')
        }
      },
      {
        "@type": "Question",
        "name": t('dok_assessment_faq_4_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('dok_assessment_faq_4_a')
        }
      },
      {
        "@type": "Question",
        "name": t('dok_assessment_faq_5_q'),
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t('dok_assessment_faq_5_a')
        }
      }
    ]
  };

  return (
    <>
      <Head>
        <title>FunBlocks AI Assessment: DOK-Based Educational Assessment Generator | Create Smart Assessments</title>
        <meta name="description" content="FunBlocks AI Assessment generates comprehensive educational assessments based on Webb's Depth of Knowledge (DOK) framework. Transform any teaching topic into professional, multi-level assessments instantly with AI." />
        <meta name="keywords" content="AI assessment generator, DOK assessment, educational assessment, Webb's Depth of Knowledge, AI teaching tools, assessment design, educational evaluation, cognitive assessment, learning assessment, teaching assessment, AI education, assessment creation, DOK framework, educational AI, smart assessment, assessment maker, teaching evaluation, learning evaluation, educational technology, AI assessment tool" />
        <link rel="canonical" href={canonicalUrl} />
        <meta property="og:title" content="FunBlocks AI Assessment: Create DOK-Based Educational Assessments | AI Assessment Generator" />
        <meta property="og:description" content="Generate comprehensive educational assessments instantly with AI. Based on Webb's DOK framework, create professional multi-level assessments from any teaching topic. Perfect for educators and instructional designers." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_dok_assessment_generated.png`} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="FunBlocks AI Assessment: DOK-Based Educational Assessment Generator" />
        <meta name="twitter:description" content="Create comprehensive educational assessments with AI. Generate DOK-based assessments from any teaching topic instantly. Professional assessment design made simple." />
        <meta name="twitter:image" content={`${siteUrl}/img/portfolio/thumbnails/aitools_dok_assessment_generated.png`} />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
        {/* FAQ structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
        />
      </Head>
      <MainFrame app={APP_TYPE.dokAssessment} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['dok-assessment', 'common'])),
    },
  }
}
