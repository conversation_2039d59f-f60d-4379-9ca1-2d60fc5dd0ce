import { Box, Flex, Button, Link, <PERSON>u, <PERSON>u<PERSON>utton, MenuList, MenuItem, IconButton, useBreakpointValue } from '@chakra-ui/react'
import { HamburgerIcon } from '@chakra-ui/icons'
import { useTranslation } from 'next-i18next'
import { useAuth } from '../contexts/AuthContext'
import LocaleSelector from './LocaleSelector'
import { GoogleLogin } from './CardGenerator/GoogleLogin'

const Header = ({ leftLink }) => {
  const { t } = useTranslation('common')
  const { user, logout, openLoginModal } = useAuth()
  const isMobile = useBreakpointValue({ base: true, md: false })

  return (
    <Flex
      as="header"
      // position="fixed"
      top={0}
      width="100%"
      // zIndex="sticky"
      bg="white"
      boxShadow="md"
      p={4}
      alignItems="center"
      justifyContent="space-between"
    >
      {
        leftLink
      }
      {
        !leftLink &&
        <Link href="/" fontSize="2xl" fontWeight="bold" color="blue.500" isExternal>
          FunBlocks AI
        </Link>
      }

      {isMobile ? (
        <Menu>
          <MenuButton
            as={IconButton}
            icon={<HamburgerIcon />}
            variant="ghost"
          />
          <MenuList>
            <MenuItem as={Link} href={process.env.PRICING_URL} isExternal>{t('pricing')}</MenuItem>
            {/* <MenuItem as={Link} href="/about">{t('about')}</MenuItem> */}
            {!user && <MenuItem onClick={openLoginModal}>{t('login')}</MenuItem>}
            {user && <MenuItem onClick={logout}>{t('logout')}</MenuItem>}
          </MenuList>
        </Menu>
      ) : (
        <Flex alignItems="center" gap={4}>
          <Link href={process.env.PRICING_URL} isExternal>{t('pricing')}</Link>
          {/* <Link href="/about">{t('about')}</Link> */}
          <LocaleSelector />
          {!user ? (
            <GoogleLogin />
          ) : (
            <Button onClick={() => {
              console.log('logout clicked........')
              logout()
            }}>{t('logout')}</Button>
          )}
        </Flex>
      )}
    </Flex>
  )
}

export default Header 