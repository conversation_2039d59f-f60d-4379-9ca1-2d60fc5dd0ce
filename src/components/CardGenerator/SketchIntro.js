import { 
  Box, 
  Container, 
  Heading, 
  Text, 
  SimpleGrid, 
  Icon, 
  VStack, 
  Accordion, 
  AccordionItem, 
  AccordionButton, 
  AccordionPanel, 
  AccordionIcon, 
  useBreakpointValue, 
  List, 
  ListItem, 
  ListIcon 
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { 
  FaPaintBrush, 
  FaMagic, 
  FaTabletAlt, 
  FaRocket, 
  FaPalette, 
  FaUserTie, 
  FaInstagram, 
  FaNewspaper,
  FaEdit,
  FaMobile
} from 'react-icons/fa'
import { 
  MdCheckCircle, 
  MdAutoFixHigh, 
  MdBrush, 
  MdColorLens, 
  MdTransform, 
  MdTouchApp,
  MdDevices,
  MdStyle
} from 'react-icons/md'
import { BiColorFill } from 'react-icons/bi'
import { AiOutlineEdit, AiOutlineBgColors } from 'react-icons/ai'
import Section from '../common/Section'

const Feature = ({ icon, title, text }) => {
  return (
    <VStack spacing={3} align="center" textAlign="center">
      <Icon as={icon} w={8} h={8} color="purple.500" />
      <Text fontWeight="semibold" fontSize="lg">{title}</Text>
      <Text color="gray.600" fontSize="md">{text}</Text>
    </VStack>
  )
}

const SketchIntro = () => {
  const { t } = useTranslation('sketch')
  const isMobile = useBreakpointValue({ base: true, md: false })

  const features = [
    {
      icon: FaTabletAlt,
      title: t('feature_1_title'),
      text: t('feature_1_text')
    },
    {
      icon: FaPalette,
      title: t('feature_2_title'),
      text: t('feature_2_text')
    },
    {
      icon: FaMagic,
      title: t('feature_3_title'),
      text: t('feature_3_text')
    },
    {
      icon: MdDevices,
      title: t('feature_4_title'),
      text: t('feature_4_text')
    }
  ]

  const drawingCapabilities = [
    {
      icon: MdTouchApp,
      title: t('drawing_capability_1_title'),
      text: t('drawing_capability_1_text')
    },
    {
      icon: FaPaintBrush,
      title: t('drawing_capability_2_title'),
      text: t('drawing_capability_2_text')
    },
    {
      icon: MdBrush,
      title: t('drawing_capability_3_title'),
      text: t('drawing_capability_3_text')
    },
    {
      icon: MdColorLens,
      title: t('drawing_capability_4_title'),
      text: t('drawing_capability_4_text')
    },
    {
      icon: MdTransform,
      title: t('drawing_capability_5_title'),
      text: t('drawing_capability_5_text')
    },
    {
      icon: MdStyle,
      title: t('drawing_capability_6_title'),
      text: t('drawing_capability_6_text')
    }
  ]

  const useCases = [
    {
      icon: FaUserTie,
      title: t('use_case_1_title'),
      text: t('use_case_1_text')
    },
    {
      icon: FaInstagram,
      title: t('use_case_2_title'),
      text: t('use_case_2_text')
    },
    {
      icon: FaNewspaper,
      title: t('use_case_3_title'),
      text: t('use_case_3_text')
    },
    {
      icon: FaEdit,
      title: t('use_case_4_title'),
      text: t('use_case_4_text')
    }
  ]

  return (
    <VStack spacing={16} width="100%" alignItems="center">
      {/* Introduction */}
      <Section bg="gray.50">
        <VStack spacing={6}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('sketch_intro_title')}
          </Heading>
          <Text
            fontSize={isMobile ? 'md' : 'lg'}
            color="gray.600"
            textAlign="center"
            maxW="4xl"
          >
            {t('sketch_intro_description')}
          </Text>
        </VStack>
      </Section>

      {/* Key Features */}
      <Section>
        <VStack spacing={8}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('key_features_title')}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} width="100%">
            {features.map((feature, index) => (
              <Feature
                key={index}
                icon={feature.icon}
                title={feature.title}
                text={feature.text}
              />
            ))}
          </SimpleGrid>
        </VStack>
      </Section>

      {/* Drawing Capabilities */}
      <Section bg="gray.50">
        <VStack spacing={8}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('drawing_capabilities_title')}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} width="100%">
            {drawingCapabilities.map((capability, index) => (
              <Feature
                key={index}
                icon={capability.icon}
                title={capability.title}
                text={capability.text}
              />
            ))}
          </SimpleGrid>
        </VStack>
      </Section>

      {/* Use Cases */}
      <Section>
        <VStack spacing={8}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('use_cases_title')}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} width="100%">
            {useCases.map((useCase, index) => (
              <Feature
                key={index}
                icon={useCase.icon}
                title={useCase.title}
                text={useCase.text}
              />
            ))}
          </SimpleGrid>
        </VStack>
      </Section>

      {/* FAQ */}
      <Section bg="gray.50">
        <VStack spacing={8}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('faq_title')}
          </Heading>
          <Accordion allowToggle width="100%" maxW="4xl">
            {[1, 2, 3, 4, 5, 6].map((num) => (
              <AccordionItem key={num}>
                <AccordionButton>
                  <Box flex="1" textAlign="left" fontWeight="semibold">
                    {t(`faq_${num}_question`)}
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel pb={4}>
                  <Text color="gray.600">
                    {t(`faq_${num}_answer`)}
                  </Text>
                </AccordionPanel>
              </AccordionItem>
            ))}
          </Accordion>
        </VStack>
      </Section>

      {/* Process Steps */}
      <Section>
        <VStack spacing={8}>
          <Heading
            size="lg"
            textAlign="center"
            bgGradient="linear(to-r, purple.400, pink.400)"
            bgClip="text"
          >
            {t('process_steps_title')}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} width="100%">
            {[1, 2, 3, 4].map((step) => (
              <VStack key={step} spacing={4} textAlign="center">
                <Box
                  w={12}
                  h={12}
                  borderRadius="full"
                  bg="purple.500"
                  color="white"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  fontSize="xl"
                  fontWeight="bold"
                >
                  {step}
                </Box>
                <VStack spacing={2}>
                  <Text fontWeight="semibold" fontSize="lg">
                    {t(`process_step_${step}_title`)}
                  </Text>
                  <Text color="gray.600" fontSize="md">
                    {t(`process_step_${step}_text`)}
                  </Text>
                </VStack>
              </VStack>
            ))}
          </SimpleGrid>
        </VStack>
      </Section>
    </VStack>
  )
}

export default SketchIntro
