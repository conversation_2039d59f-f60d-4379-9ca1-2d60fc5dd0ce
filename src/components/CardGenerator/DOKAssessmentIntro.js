import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Badge, HStack } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaBrain, FaGraduationCap, FaChalkboardTeacher, FaUserGraduate, FaUsers, FaLightbulb, FaChartLine, FaLayerGroup, FaMapMarkedAlt, FaFileAlt, FaClock, FaCheckCircle, FaRocket, FaCog } from 'react-icons/fa'
import { MdCheckCircle, MdSchool, MdTimeline, MdAutoGraph, MdPsychology, MdOutlineAssignment, MdOutlineStairs, MdSettings, MdLayers, MdAssessment, MdSpeed, MdHighQuality, MdTune } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const AdvantageCard = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="blue.50"
            rounded="xl"
            shadow="sm"
            borderWidth="1px"
            borderColor="blue.100"
            spacing={4}
        >
            <Icon as={icon} w={8} h={8} color="blue.600" />
            <Text fontWeight="bold" fontSize="lg" color="blue.700">{title}</Text>
            <Text color="gray.700">{text}</Text>
        </VStack>
    )
}

const StepCard = ({ title, text, icon, stepNumber }) => {
    return (
        <VStack
            align={'center'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
            position="relative"
        >
            <Badge
                position="absolute"
                top="-10px"
                left="50%"
                transform="translateX(-50%)"
                bg="blue.500"
                color="white"
                borderRadius="full"
                px={3}
                py={1}
                fontSize="sm"
            >
                {stepNumber}
            </Badge>
            <Icon as={icon} w={8} h={8} color="blue.500" mt={2} />
            <Text fontWeight="bold" fontSize="lg" textAlign="center">{title}</Text>
            <Text color="gray.600" textAlign="center">{text}</Text>
        </VStack>
    )
}

const DOKAssessmentIntro = () => {
    const { t } = useTranslation('dok-assessment')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaRocket,
            title: t('dok_assessment_feature_1_title'),
            text: t('dok_assessment_feature_1_text')
        },
        {
            icon: MdLayers,
            title: t('dok_assessment_feature_2_title'),
            text: t('dok_assessment_feature_2_text')
        },
        {
            icon: MdHighQuality,
            title: t('dok_assessment_feature_3_title'),
            text: t('dok_assessment_feature_3_text')
        }
    ]

    const advantages = [
        {
            icon: FaBrain,
            title: t('dok_assessment_advantage_1_title'),
            text: t('dok_assessment_advantage_1_text')
        },
        {
            icon: MdAutoGraph,
            title: t('dok_assessment_advantage_2_title'),
            text: t('dok_assessment_advantage_2_text')
        },
        {
            icon: FaFileAlt,
            title: t('dok_assessment_advantage_3_title'),
            text: t('dok_assessment_advantage_3_text')
        },
        {
            icon: MdTune,
            title: t('dok_assessment_advantage_4_title'),
            text: t('dok_assessment_advantage_4_text')
        }
    ]

    const targetAudiences = [
        {
            icon: FaChalkboardTeacher,
            title: t('dok_assessment_audience_1_title'),
            text: t('dok_assessment_audience_1_text')
        },
        {
            icon: MdOutlineAssignment,
            title: t('dok_assessment_audience_2_title'),
            text: t('dok_assessment_audience_2_text')
        },
        {
            icon: MdSchool,
            title: t('dok_assessment_audience_3_title'),
            text: t('dok_assessment_audience_3_text')
        },
        {
            icon: FaUsers,
            title: t('dok_assessment_audience_4_title'),
            text: t('dok_assessment_audience_4_text')
        }
    ]

    const howItWorksSteps = [
        {
            icon: MdSettings,
            title: t('dok_assessment_step_1_title'),
            text: t('dok_assessment_step_1_text')
        },
        {
            icon: FaBrain,
            title: t('dok_assessment_step_2_title'),
            text: t('dok_assessment_step_2_text')
        },
        {
            icon: MdAssessment,
            title: t('dok_assessment_step_3_title'),
            text: t('dok_assessment_step_3_text')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <Text
                    fontSize={isMobile ? 'md' : 'lg'}
                    color="gray.600"
                    textAlign="center"
                    maxW="2xl"
                    mb={6}
                >
                    {t('dok_assessment_intro_enhanced_description')}
                </Text>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* What is DOK Assessment */}
            <Section bg="white">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('why_dok_assessment_description_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('why_dok_assessment_description')}
                    </Text>
                </VStack>
            </Section>

            {/* Benefits */}
            <Section bg="blue.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_assessment_benefits_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} width="100%">
                        {[1, 2, 3, 4].map((i) => (
                            <HStack key={i} align="start" spacing={4}>
                                <Icon as={MdCheckCircle} color="green.500" w={6} h={6} mt={1} />
                                <Text fontSize={isMobile ? 'md' : 'lg'} color="gray.700">
                                    {t(`dok_assessment_benefit_${i}`)}
                                </Text>
                            </HStack>
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Advantages */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_assessment_advantages_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                        {advantages.map((advantage, index) => (
                            <AdvantageCard
                                key={index}
                                icon={advantage.icon}
                                title={advantage.title}
                                text={advantage.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Target Audience */}
            <Section bg="gray.50">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_assessment_target_audience_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                        {targetAudiences.map((audience, index) => (
                            <Feature
                                key={index}
                                icon={audience.icon}
                                title={audience.title}
                                text={audience.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* How It Works */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_assessment_how_it_works_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                        {howItWorksSteps.map((step, index) => (
                            <StepCard
                                key={index}
                                icon={step.icon}
                                title={step.title}
                                text={step.text}
                                stepNumber={index + 1}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Perfect For */}
            <Section bg="blue.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('dok_assessment_uses_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} width="100%">
                        {[1, 2, 3, 4].map((i) => (
                            <HStack key={i} align="start" spacing={4}>
                                <Icon as={FaCheckCircle} color="blue.500" w={6} h={6} mt={1} />
                                <Text fontSize={isMobile ? 'md' : 'lg'} color="gray.700">
                                    {t(`dok_assessment_use_${i}`)}
                                </Text>
                            </HStack>
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="dok-assessment" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`dok_assessment_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`dok_assessment_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default DOKAssessmentIntro
