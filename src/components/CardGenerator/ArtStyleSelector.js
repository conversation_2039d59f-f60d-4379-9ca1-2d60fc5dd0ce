import React, { useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>l,
  HStack,
  Select
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

const styleOptions = [
  { id: 'sketch_styles_popular_title', type: 'title' },
  { id: 'style_realistic', type: 'option', value: 'realistic' },
  { id: 'style_anime', type: 'option', value: 'anime' },
  { id: 'style_digital_art', type: 'option', value: 'digital_art' },
  { id: 'style_watercolor', type: 'option', value: 'watercolor' },

  { id: 'sketch_styles_classic_title', type: 'title' },
  { id: 'style_oil_painting', type: 'option', value: 'oil_painting' },
  { id: 'style_pencil_sketch', type: 'option', value: 'pencil_sketch' },
  { id: 'style_impressionist', type: 'option', value: 'impressionist' },
  { id: 'style_renaissance', type: 'option', value: 'renaissance' },

  { id: 'sketch_styles_modern_title', type: 'title' },
  { id: 'style_pop_art', type: 'option', value: 'pop_art' },
  { id: 'style_art_nouveau', type: 'option', value: 'art_nouveau' },
  { id: 'style_cyberpunk', type: 'option', value: 'cyberpunk' },
  { id: 'style_vaporwave', type: 'option', value: 'vaporwave' },

  { id: 'sketch_styles_stylized_title', type: 'title' },
  { id: 'style_cartoon', type: 'option', value: 'cartoon' },
  { id: 'style_comic', type: 'option', value: 'comic' },
  { id: 'style_fantasy', type: 'option', value: 'fantasy' },
  { id: 'style_gothic', type: 'option', value: 'gothic' },

  { id: 'sketch_styles_minimal_title', type: 'title' },
  { id: 'style_minimalist', type: 'option', value: 'minimalist' },
  { id: 'style_line_art', type: 'option', value: 'line_art' },
  { id: 'style_abstract', type: 'option', value: 'abstract' },
  { id: 'style_geometric', type: 'option', value: 'geometric' }
];

const ArtStyleSelector = ({ onSelect, value }) => {
  const { t } = useTranslation('sketch');

  useEffect(() => {
    if (!value && styleOptions?.length) {
      // Set default to first option (realistic)
      onSelect('realistic');
    }
  }, [value, onSelect]);

  return (
    <HStack width={'100%'}>
      <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>
        {t('choose_art_style')}
      </FormLabel>
      <Select
        bg='white'
        variant={'ghost'}
        onChange={(e) => onSelect(t('style_' + e.target.value) + ' - ' + t('style_' + e.target.value + '_desc'))}
        value={value}
        isRequired
      >
        {styleOptions.map((option) => {
          return (
            <option
              key={option.id}
              value={option.value || option.id}
              disabled={option.type === 'title'}
            >
              {t(option.id) + ' - ' + t(option.id + '_desc')}
            </option>
          )
        })}
      </Select>
    </HStack>
  );
};

export default ArtStyleSelector;
