import React, { useRef, useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Button, 
  HStack, 
  VStack, 
  IconButton, 
  Slider, 
  SliderTrack, 
  SliderFilledTrack, 
  SliderThumb,
  Text,
  useColorModeValue,
  Tooltip
} from '@chakra-ui/react';
import { FaEraser, FaPen, FaUndo, FaTrash } from 'react-icons/fa';
import { useTranslation } from 'next-i18next';

const SketchCanvas = ({ onSketchChange, isDisabled = false }) => {
  const { t } = useTranslation('sketch');
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState('pen'); // 'pen' or 'eraser'
  const [brushSize, setBrushSize] = useState(5);
  const [paths, setPaths] = useState([]);
  const [currentPath, setCurrentPath] = useState([]);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    
    // Clear canvas with white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }, []);

  // Get coordinates from mouse or touch event
  const getCoordinates = useCallback((event) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    
    let clientX, clientY;
    if (event.touches) {
      clientX = event.touches[0].clientX;
      clientY = event.touches[0].clientY;
    } else {
      clientX = event.clientX;
      clientY = event.clientY;
    }
    
    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  }, []);

  // Start drawing
  const startDrawing = useCallback((event) => {
    if (isDisabled) return;
    
    event.preventDefault();
    setIsDrawing(true);
    
    const coords = getCoordinates(event);
    const newPath = {
      tool,
      brushSize,
      points: [coords]
    };
    
    setCurrentPath(newPath);
  }, [isDisabled, tool, brushSize, getCoordinates]);

  // Continue drawing
  const draw = useCallback((event) => {
    if (!isDrawing || isDisabled) return;
    
    event.preventDefault();
    const coords = getCoordinates(event);
    
    setCurrentPath(prev => ({
      ...prev,
      points: [...prev.points, coords]
    }));
    
    // Draw on canvas
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (tool === 'eraser') {
      ctx.globalCompositeOperation = 'destination-out';
    } else {
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = '#000000';
    }
    
    ctx.lineWidth = brushSize;
    ctx.beginPath();
    
    const points = currentPath.points;
    if (points.length > 1) {
      const lastPoint = points[points.length - 2];
      const currentPoint = points[points.length - 1];
      
      ctx.moveTo(lastPoint.x, lastPoint.y);
      ctx.lineTo(currentPoint.x, currentPoint.y);
      ctx.stroke();
    }
  }, [isDrawing, isDisabled, tool, brushSize, currentPath, getCoordinates]);

  // Stop drawing
  const stopDrawing = useCallback(() => {
    if (!isDrawing) return;
    
    setIsDrawing(false);
    
    if (currentPath.points.length > 0) {
      setPaths(prev => [...prev, currentPath]);
      setCurrentPath([]);
      
      // Convert canvas to blob and notify parent
      const canvas = canvasRef.current;
      canvas.toBlob((blob) => {
        if (onSketchChange) {
          onSketchChange(blob);
        }
      }, 'image/png');
    }
  }, [isDrawing, currentPath, onSketchChange]);

  // Clear canvas
  const clearCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    setPaths([]);
    setCurrentPath([]);
    
    if (onSketchChange) {
      onSketchChange(null);
    }
  }, [onSketchChange]);

  // Undo last path
  const undo = useCallback(() => {
    if (paths.length === 0) return;
    
    const newPaths = paths.slice(0, -1);
    setPaths(newPaths);
    
    // Redraw canvas
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Clear and fill with white
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Redraw all paths
    newPaths.forEach(path => {
      if (path.tool === 'eraser') {
        ctx.globalCompositeOperation = 'destination-out';
      } else {
        ctx.globalCompositeOperation = 'source-over';
        ctx.strokeStyle = '#000000';
      }
      
      ctx.lineWidth = path.brushSize;
      ctx.beginPath();
      
      path.points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y);
        } else {
          ctx.lineTo(point.x, point.y);
        }
      });
      ctx.stroke();
    });
    
    // Update parent
    canvas.toBlob((blob) => {
      if (onSketchChange) {
        onSketchChange(blob);
      }
    }, 'image/png');
  }, [paths, onSketchChange]);

  return (
    <VStack spacing={4} width="100%">
      {/* Tools */}
      <HStack spacing={4} flexWrap="wrap" justify="center">
        <HStack spacing={2}>
          <Tooltip label={t('pen_tool')}>
            <IconButton
              icon={<FaPen />}
              colorScheme={tool === 'pen' ? 'blue' : 'gray'}
              variant={tool === 'pen' ? 'solid' : 'outline'}
              onClick={() => setTool('pen')}
              isDisabled={isDisabled}
              size="sm"
            />
          </Tooltip>
          
          <Tooltip label={t('eraser_tool')}>
            <IconButton
              icon={<FaEraser />}
              colorScheme={tool === 'eraser' ? 'red' : 'gray'}
              variant={tool === 'eraser' ? 'solid' : 'outline'}
              onClick={() => setTool('eraser')}
              isDisabled={isDisabled}
              size="sm"
            />
          </Tooltip>
        </HStack>
        
        <HStack spacing={2} minW="120px">
          <Text fontSize="sm">{t('brush_size')}</Text>
          <Slider
            value={brushSize}
            onChange={setBrushSize}
            min={1}
            max={20}
            step={1}
            width="80px"
            isDisabled={isDisabled}
          >
            <SliderTrack>
              <SliderFilledTrack />
            </SliderTrack>
            <SliderThumb />
          </Slider>
          <Text fontSize="sm" minW="20px">{brushSize}</Text>
        </HStack>
        
        <HStack spacing={2}>
          <Tooltip label={t('undo')}>
            <IconButton
              icon={<FaUndo />}
              onClick={undo}
              isDisabled={isDisabled || paths.length === 0}
              size="sm"
            />
          </Tooltip>
          
          <Tooltip label={t('clear_canvas')}>
            <IconButton
              icon={<FaTrash />}
              colorScheme="red"
              variant="outline"
              onClick={clearCanvas}
              isDisabled={isDisabled}
              size="sm"
            />
          </Tooltip>
        </HStack>
      </HStack>
      
      {/* Canvas */}
      <Box
        position="relative"
        width="100%"
        maxWidth="600px"
        height="400px"
        border="2px solid"
        borderColor={borderColor}
        borderRadius="md"
        bg={bgColor}
        overflow="hidden"
      >
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            cursor: isDisabled ? 'not-allowed' : (tool === 'pen' ? 'crosshair' : 'grab'),
            touchAction: 'none'
          }}
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
        />
      </Box>
      
      <Text fontSize="sm" color="gray.500" textAlign="center">
        {t('canvas_instruction')}
      </Text>
    </VStack>
  );
};

export default SketchCanvas;
