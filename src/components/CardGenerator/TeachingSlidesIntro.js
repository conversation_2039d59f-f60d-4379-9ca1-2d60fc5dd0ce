import React from 'react'
import {
    VStack,
    Heading,
    Text,
    SimpleGrid,
    Box,
    Badge,
    Accordion,
    AccordionItem,
    AccordionButton,
    AccordionPanel,
    AccordionIcon,
    useBreakpointValue,
    Container,
    Flex,
    Icon,
    Stack,
    HStack
} from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import {
    FaBrain,
    FaChalkboardTeacher,
    FaLayerGroup,
    FaGraduationCap,
    FaRocket,
    FaLightbulb,
    FaUsers,
    FaChartLine,
    FaBookOpen,
    FaCogs
} from 'react-icons/fa'
import Section from '../common/Section'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const TeachingSlidesIntro = () => {
    const { t } = useTranslation('teaching-slides')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const mainFeatures = [
        {
            icon: FaBrain,
            title: t('teaching_slides_feature_1_title'),
            text: t('teaching_slides_feature_1_text')
        },
        {
            icon: FaChalkboardTeacher,
            title: t('teaching_slides_feature_2_title'),
            text: t('teaching_slides_feature_2_text')
        },
        {
            icon: FaLayerGroup,
            title: t('teaching_slides_feature_3_title'),
            text: t('teaching_slides_feature_3_text')
        },
        {
            icon: FaGraduationCap,
            title: t('teaching_slides_feature_4_title'),
            text: t('teaching_slides_feature_4_text')
        }
    ]

    const advantages = [
        {
            icon: FaRocket,
            title: t('advantage_1_title'),
            text: t('advantage_1_text')
        },
        {
            icon: FaLightbulb,
            title: t('advantage_2_title'),
            text: t('advantage_2_text')
        },
        {
            icon: FaCogs,
            title: t('advantage_3_title'),
            text: t('advantage_3_text')
        }
    ]

    const frameworks = [
        {
            title: t('framework_1_title'),
            text: t('framework_1_text')
        },
        {
            title: t('framework_2_title'),
            text: t('framework_2_text')
        },
        {
            title: t('framework_3_title'),
            text: t('framework_3_text')
        },
        {
            title: t('framework_4_title'),
            text: t('framework_4_text')
        },
        {
            title: t('framework_5_title'),
            text: t('framework_5_text')
        },
        {
            title: t('framework_6_title'),
            text: t('framework_6_text')
        }
    ]

    const benefits = [
        t('teaching_slides_benefit_1'),
        t('teaching_slides_benefit_2'),
        t('teaching_slides_benefit_3'),
        t('teaching_slides_benefit_4')
    ]

    const useCases = [
        t('teaching_slides_use_1'),
        t('teaching_slides_use_2'),
        t('teaching_slides_use_3'),
        t('teaching_slides_use_4')
    ]

    return (
        <VStack spacing={0} width="100%" mt={isMobile ? 6 : 16}>
            {/* Hero Section */}
            <Box
                w="100%"
                bgGradient="linear(to-br, blue.50, purple.50)"
                py={isMobile ? 12 : 16}
                px={4}
                borderBottomWidth="1px"
                borderBottomColor="gray.200"
            >
                <VStack spacing={8} maxW="4xl" mx="auto" textAlign="center">
                    <Badge colorScheme="blue" fontSize={isMobile ? "sm" : "md"} px={3} py={1} borderRadius="full">
                        AI Educational Design
                    </Badge>

                    <Heading
                        fontSize={isMobile ? "3xl" : "5xl"}
                        fontWeight="extrabold"
                        bgGradient="linear(to-r, blue.400, purple.500)"
                        bgClip="text"
                        lineHeight="1.2"
                    >
                        {t('teaching_slides_intro_title')}
                    </Heading>

                    <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl">
                        {t('teaching_slides_intro_enhanced_description')}
                    </Text>
                </VStack>
            </Box>

            {/* Main Features */}
            <Section bg="white" title={t('teaching_slides_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    {mainFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* What is AI Educational Slide Design */}
            <Section bg="gray.50">
                <VStack spacing={6} textAlign="center" maxW="3xl" mx="auto">
                    <Heading size="lg" color="gray.800">
                        {t('why_teaching_slides_description_title')}
                    </Heading>
                    <Text fontSize="lg" color="gray.600" lineHeight="1.8">
                        {t('why_teaching_slides_description')}
                    </Text>
                </VStack>
            </Section>

            {/* Product Advantages */}
            <Section bg="white" title={t('teaching_slides_advantages_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {advantages.map((advantage, index) => (
                        <Feature
                            key={index}
                            icon={advantage.icon}
                            title={advantage.title}
                            text={advantage.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* Supported Teaching Frameworks */}
            <Section bg="blue.50" title={t('frameworks_title')}>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                    {frameworks.map((framework, index) => (
                        <Box
                            key={index}
                            bg="white"
                            p={6}
                            borderRadius="lg"
                            boxShadow="sm"
                            borderWidth="1px"
                            borderColor="gray.200"
                            _hover={{ boxShadow: "md", transform: "translateY(-2px)" }}
                            transition="all 0.2s"
                        >
                            <VStack spacing={3} align="start">
                                <Heading size="md" color="blue.600">
                                    {framework.title}
                                </Heading>
                                <Text color="gray.600" fontSize="sm">
                                    {framework.text}
                                </Text>
                            </VStack>
                        </Box>
                    ))}
                </SimpleGrid>
            </Section>

            {/* Benefits List */}
            <Section bg="white" title={t('teaching_slides_benefits_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} maxW="4xl" mx="auto">
                    {benefits.map((benefit, index) => (
                        <HStack key={index} align="start" spacing={3}>
                            <Icon as={FaChartLine} color="green.500" mt={1} />
                            <Text color="gray.700">{benefit}</Text>
                        </HStack>
                    ))}
                </SimpleGrid>
            </Section>

            {/* Target Users */}
            <Section bg="gray.50" title={t('teaching_slides_uses_title')}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} maxW="4xl" mx="auto">
                    {useCases.map((useCase, index) => (
                        <HStack key={index} align="start" spacing={3}>
                            <Icon as={FaUsers} color="purple.500" mt={1} />
                            <Text color="gray.700">{useCase}</Text>
                        </HStack>
                    ))}
                </SimpleGrid>
            </Section>

            {/* Why Not ChatGPT Section */}
            <Section bg="white" title={t('why_not_chatgpt_title')}>
                <VStack spacing={8} maxW="4xl" mx="auto">
                    <VStack spacing={4} textAlign="center">
                        <Heading size="md" color="red.600">
                            {t('common_mistakes_title')}
                        </Heading>
                        <Text color="gray.600" fontSize="lg" lineHeight="1.8">
                            {t('chatgpt_teaching_problems')}
                        </Text>
                    </VStack>
                    
                    <VStack spacing={4} textAlign="center">
                        <Heading size="md" color="blue.600">
                            {t('funblocks_advantages_title')}
                        </Heading>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="100%">
                            {[1, 2, 3, 4].map((i) => (
                                <HStack key={i} align="start" spacing={3}>
                                    <Icon as={FaBookOpen} color="blue.500" mt={1} />
                                    <Text color="gray.700">{t(`funblocks_advantage_${i}`)}</Text>
                                </HStack>
                            ))}
                        </SimpleGrid>
                    </VStack>
                </VStack>
            </Section>

            {/* How It Works */}
            <Section bg="gray.50" title={t('how_it_works_title')}>
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {[1, 2, 3].map((i) => (
                        <VStack key={i} spacing={4} textAlign="center">
                            <Box
                                w={16}
                                h={16}
                                bg="blue.100"
                                borderRadius="full"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                            >
                                <Icon as={FaGraduationCap} w={8} h={8} color="blue.600" />
                            </Box>
                            <Heading size="md" color="gray.800">
                                {t(`content_type_${i}_title`)}
                            </Heading>
                            <Text color="gray.600" fontSize="sm">
                                {t(`content_type_${i}_text`)}
                            </Text>
                        </VStack>
                    ))}
                </SimpleGrid>
            </Section>

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={isMobile ? 4 : 8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`teaching_slides_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`teaching_slides_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default TeachingSlidesIntro
