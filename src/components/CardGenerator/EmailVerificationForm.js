import React, { useState, useEffect } from 'react';
import {
    VStack,
    Input,
    Button,
    FormControl,
    FormLabel,
    FormErrorMessage,
    HStack,
    PinInput,
    PinInputField,
    useToast,
} from '@chakra-ui/react';
import { apiUrl } from '../../utils/apiUtils';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'next-i18next';

const EmailVerificationForm = ({ }) => {
    const { t } = useTranslation('common');
    const [email, setEmail] = useState('');
    const [verificationCode, setVerificationCode] = useState('');
    const [isEmailSent, setIsEmailSent] = useState(false);
    const [error, setError] = useState('');
    const { logon } = useAuth();
    const toast = useToast();
    const [isLoading, setIsLoading] = useState(false);

    const [isRecaptchaLoaded, setIsRecaptchaLoaded] = useState(false);

    useEffect(() => {
        // Check if reCAPTCHA script is already loaded
        if (window.grecaptcha && window.grecaptcha.enterprise) {
            setIsRecaptchaLoaded(true);
            return;
        }

        // Load the reCAPTCHA v3 script
        const script = document.createElement('script');
        script.src = 'https://www.google.com/recaptcha/enterprise.js?render=6LcolWYqAAAAAJuipe7B6EKECW5E_pcjHEDSXfaw';
        script.async = true;

        script.onload = () => {
            // Wait for grecaptcha to be available
            const checkRecaptcha = () => {
                if (window.grecaptcha && window.grecaptcha.enterprise) {
                    setIsRecaptchaLoaded(true);
                } else {
                    setTimeout(checkRecaptcha, 100);
                }
            };
            checkRecaptcha();
        };

        script.onerror = () => {
            console.error('Failed to load reCAPTCHA script');
        };

        document.body.appendChild(script);

        return () => {
            // Only remove if we added it
            if (document.body.contains(script)) {
                document.body.removeChild(script);
            }
        };
    }, []);

    const executeRecaptcha = () => {
        return new Promise((resolve, reject) => {
            if (!window.grecaptcha || !window.grecaptcha.enterprise) {
                reject(new Error('reCAPTCHA not loaded'));
                return;
            }

            window.grecaptcha.enterprise.ready(() => {
                window.grecaptcha.enterprise.execute('6LcolWYqAAAAAJuipe7B6EKECW5E_pcjHEDSXfaw', { action: 'SEND_EMAIL_VERIFICATION_CODE' })
                    .then(token => resolve(token))
                    .catch(error => reject(error));
            });
        });
    };

    const handleSendVerificationCode = async () => {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            setError(t('please_enter_valid_email'));
            return;
        }

        if (!isRecaptchaLoaded) {
            toast({
                title: t('failed_to_send_verification_code'),
                description: 'reCAPTCHA is still loading, please try again in a moment',
                status: "error",
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        setIsLoading(true);
        try {
            const recaptchaToken = await executeRecaptcha();
            const response = await fetch(`${apiUrl}/users/verify?` + new URLSearchParams({
                username: email,
                type: 'login',
                recaptchaToken: recaptchaToken,
            }));
            if (response.ok) {
                let resp = await response.json();

                toast({
                    title: resp.message,
                    status: "success",
                    duration: 3000,
                    isClosable: true,
                });
                setIsEmailSent(true);
            } else {
                throw new Error(t('failed_to_send_verification_code'));
            }
        } catch (error) {
            toast({
                title: t('failed_to_send_verification_code'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`${apiUrl}/users/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: email,
                    vcode: verificationCode,
                    mode: 'temp_token',
                }),
            });

            if (response.ok) {
                const data = await response.json();
                logon(data.data);
            } else {
                throw new Error(t('verification_failed'));
            }
        } catch (error) {
            toast({
                title: t('verification_failed'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <VStack spacing={4}>
            {!isEmailSent ? (
                <>
                    <FormControl isInvalid={!!error}>
                        <FormLabel>{t('email_address')}</FormLabel>
                        <Input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder={t('enter_your_email')}
                        />
                        <FormErrorMessage>{error}</FormErrorMessage>
                    </FormControl>
                    <Button
                        onClick={handleSendVerificationCode}
                        isLoading={isLoading || !isRecaptchaLoaded}
                        loadingText={!isRecaptchaLoaded ? 'Loading...' : t('sending')}
                        isDisabled={!isRecaptchaLoaded}
                    >
                        {t('send_verification_code')}
                    </Button>
                </>
            ) : (
                <>
                    <HStack>
                        <PinInput onChange={(value) => setVerificationCode(value)}>
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                        </PinInput>
                    </HStack>
                    <Button
                        onClick={handleSubmit}
                        isLoading={isLoading}
                        isDisabled={isLoading}
                        loadingText={t('verifying')}
                    >
                        {t('verify_and_login')}
                    </Button>
                </>
            )}
        </VStack>
    );
};

export default EmailVerificationForm;
