import React, { useState, useEffect } from 'react';
import {
    VStack,
    Input,
    Button,
    FormControl,
    FormLabel,
    FormErrorMessage,
    HStack,
    PinInput,
    PinInputField,
    useToast,
    Box,
    Image,
    Text,
    Spinner,
} from '@chakra-ui/react';
import { apiUrl } from '../../utils/apiUtils';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'next-i18next';

const EmailVerificationForm = ({ }) => {
    const { t } = useTranslation('common');
    const [email, setEmail] = useState('');
    const [verificationCode, setVerificationCode] = useState('');
    const [isEmailSent, setIsEmailSent] = useState(false);
    const [error, setError] = useState('');
    const { logon } = useAuth();
    const toast = useToast();
    const [isLoading, setIsLoading] = useState(false);

    // 图片验证码相关状态
    const [captchaInput, setCaptchaInput] = useState('');
    const [captchaUrl, setCaptchaUrl] = useState('');
    const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
    const [captchaError, setCaptchaError] = useState('');
    const [isCaptchaVerifying, setIsCaptchaVerifying] = useState(false);

    // 加载验证码图片
    const loadCaptcha = async () => {
        try {
            const timestamp = new Date().getTime();
            setCaptchaUrl(`${apiUrl}/users/captcha?t=${timestamp}`);
            setIsCaptchaVerified(false);
            setCaptchaInput('');
            setCaptchaError('');
            setIsCaptchaVerifying(false);
        } catch (error) {
            console.error('Failed to load captcha:', error);
        }
    };

    useEffect(() => {
        loadCaptcha();
    }, []);

    // 验证图片验证码
    const verifyCaptcha = async () => {
        if (!captchaInput.trim() || captchaInput.length !== 4) {
            return false;
        }

        setIsCaptchaVerifying(true);
        setCaptchaError('');

        try {
            const response = await fetch(`${apiUrl}/users/verifyCaptcha?captcha=${encodeURIComponent(captchaInput)}`);
            const result = await response.json();

            if (result.success && result.data.verified) {
                setIsCaptchaVerified(true);
                setCaptchaError('');
                return true;
            } else {
                setCaptchaError(t('captcha_incorrect'));
                setIsCaptchaVerified(false);
                // 重新加载验证码
                loadCaptcha();
                return false;
            }
        } catch (error) {
            console.error('Captcha verification failed:', error);
            setCaptchaError(t('captcha_verification_failed'));
            setIsCaptchaVerified(false);
            return false;
        } finally {
            setIsCaptchaVerifying(false);
        }
    };

    // 处理验证码输入变化
    const handleCaptchaInputChange = (e) => {
        const value = e.target.value;
        setCaptchaInput(value);

        // 重置验证状态
        if (isCaptchaVerified) {
            setIsCaptchaVerified(false);
        }
        if (captchaError) {
            setCaptchaError('');
        }

        // 当输入4个字符时自动验证
        if (value.length === 4) {
            // 直接调用验证函数，传入当前值
            verifyCaptchaWithValue(value);
        }
    };

    // 使用指定值验证验证码
    const verifyCaptchaWithValue = async (inputValue) => {
        if (!inputValue || inputValue.length !== 4) {
            return false;
        }

        setIsCaptchaVerifying(true);
        setCaptchaError('');

        try {
            const response = await fetch(`${apiUrl}/verifyCaptcha?captcha=${encodeURIComponent(inputValue)}`);
            const result = await response.json();

            if (result.success && result.data.verified) {
                setIsCaptchaVerified(true);
                setCaptchaError('');
                return true;
            } else {
                setCaptchaError(t('captcha_incorrect'));
                setIsCaptchaVerified(false);
                // 重新加载验证码
                loadCaptcha();
                return false;
            }
        } catch (error) {
            console.error('Captcha verification failed:', error);
            setCaptchaError(t('captcha_verification_failed'));
            setIsCaptchaVerified(false);
            return false;
        } finally {
            setIsCaptchaVerifying(false);
        }
    };

    const handleSendVerificationCode = async () => {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            setError(t('please_enter_valid_email'));
            return;
        }

        if (!isCaptchaVerified) {
            toast({
                title: t('failed_to_send_verification_code'),
                description: t('please_verify_captcha_first'),
                status: "error",
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch(`${apiUrl}/users/verify?` + new URLSearchParams({
                username: email,
                type: 'login',
            }));
            if (response.ok) {
                let resp = await response.json();

                toast({
                    title: resp.message,
                    status: "success",
                    duration: 3000,
                    isClosable: true,
                });
                setIsEmailSent(true);
            } else {
                throw new Error(t('failed_to_send_verification_code'));
            }
        } catch (error) {
            toast({
                title: t('failed_to_send_verification_code'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`${apiUrl}/users/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: email,
                    vcode: verificationCode,
                    mode: 'temp_token',
                }),
            });

            if (response.ok) {
                const data = await response.json();
                logon(data.data);
            } else {
                throw new Error(t('verification_failed'));
            }
        } catch (error) {
            toast({
                title: t('verification_failed'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <VStack spacing={4}>
            {!isEmailSent ? (
                <>
                    {/* 图片验证码 - 放在最前面 */}
                    <FormControl isInvalid={!!captchaError}>
                        <FormLabel>{t('captcha_verification')}</FormLabel>
                        <HStack spacing={3}>
                            <Input
                                value={captchaInput}
                                onChange={handleCaptchaInputChange}
                                placeholder={t('enter_captcha')}
                                maxLength={4}
                                flex={1}
                                isDisabled={isCaptchaVerifying}
                            />
                            {isCaptchaVerifying && (
                                <Spinner size="sm" color="blue.500" />
                            )}
                            <Box
                                cursor="pointer"
                                onClick={loadCaptcha}
                                title={t('click_to_refresh')}
                                border="1px solid"
                                borderColor="gray.200"
                                borderRadius="md"
                                p={1}
                                minW="120px"
                                h="40px"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                            >
                                {captchaUrl ? (
                                    <Image
                                        src={captchaUrl}
                                        alt="Captcha"
                                        maxH="38px"
                                        maxW="118px"
                                    />
                                ) : (
                                    <Text fontSize="sm" color="gray.500">
                                        {t('loading')}...
                                    </Text>
                                )}
                            </Box>
                        </HStack>
                        <FormErrorMessage>{captchaError}</FormErrorMessage>
                        {isCaptchaVerified && (
                            <Text fontSize="sm" color="green.500" mt={1}>
                                ✓ {t('captcha_verified')}
                            </Text>
                        )}
                    </FormControl>

                    <FormControl isInvalid={!!error}>
                        <FormLabel>{t('email_address')}</FormLabel>
                        <Input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder={t('enter_your_email')}
                            isDisabled={isCaptchaVerifying || !isCaptchaVerified}
                        />
                        <FormErrorMessage>{error}</FormErrorMessage>
                    </FormControl>

                    <Button
                        onClick={handleSendVerificationCode}
                        isLoading={isLoading}
                        loadingText={t('sending')}
                        isDisabled={!email.trim() || !isCaptchaVerified || isCaptchaVerifying}
                    >
                        {t('send_verification_code')}
                    </Button>
                </>
            ) : (
                <>
                    <HStack>
                        <PinInput onChange={(value) => setVerificationCode(value)}>
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                        </PinInput>
                    </HStack>
                    <Button
                        onClick={handleSubmit}
                        isLoading={isLoading}
                        isDisabled={isLoading}
                        loadingText={t('verifying')}
                    >
                        {t('verify_and_login')}
                    </Button>
                </>
            )}
        </VStack>
    );
};

export default EmailVerificationForm;
