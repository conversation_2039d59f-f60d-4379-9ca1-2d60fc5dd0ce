import React, { useState, useEffect } from 'react';
import {
    VStack,
    Input,
    Button,
    FormControl,
    FormLabel,
    FormErrorMessage,
    HStack,
    PinInput,
    PinInputField,
    useToast,
    Box,
    Image,
    Text,
} from '@chakra-ui/react';
import { apiUrl } from '../../utils/apiUtils';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'next-i18next';

const EmailVerificationForm = ({ }) => {
    const { t } = useTranslation('common');
    const [email, setEmail] = useState('');
    const [verificationCode, setVerificationCode] = useState('');
    const [isEmailSent, setIsEmailSent] = useState(false);
    const [error, setError] = useState('');
    const { logon } = useAuth();
    const toast = useToast();
    const [isLoading, setIsLoading] = useState(false);

    // 图片验证码相关状态
    const [captchaInput, setCaptchaInput] = useState('');
    const [captchaUrl, setCaptchaUrl] = useState('');
    const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
    const [captchaError, setCaptchaError] = useState('');

    // 加载验证码图片
    const loadCaptcha = async () => {
        try {
            const timestamp = new Date().getTime();
            setCaptchaUrl(`${apiUrl}/users/captcha?t=${timestamp}`);
            setIsCaptchaVerified(false);
            setCaptchaInput('');
            setCaptchaError('');
        } catch (error) {
            console.error('Failed to load captcha:', error);
        }
    };

    useEffect(() => {
        loadCaptcha();
    }, []);

    // 验证图片验证码
    const verifyCaptcha = async () => {
        if (!captchaInput.trim()) {
            setCaptchaError(t('please_enter_captcha'));
            return false;
        }

        try {
            const response = await fetch(`${apiUrl}/users/verifyCaptcha?captcha=${encodeURIComponent(captchaInput)}`);
            const result = await response.json();

            if (result.success && result.data.verified) {
                setIsCaptchaVerified(true);
                setCaptchaError('');
                return true;
            } else {
                setCaptchaError(t('captcha_incorrect'));
                setIsCaptchaVerified(false);
                // 重新加载验证码
                loadCaptcha();
                return false;
            }
        } catch (error) {
            console.error('Captcha verification failed:', error);
            setCaptchaError(t('captcha_verification_failed'));
            setIsCaptchaVerified(false);
            return false;
        }
    };

    const handleSendVerificationCode = async () => {
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            setError(t('please_enter_valid_email'));
            return;
        }

        // 先验证图片验证码
        if (!isCaptchaVerified) {
            const verified = await verifyCaptcha();
            if (!verified) {
                return;
            }
        }

        setIsLoading(true);
        try {
            const response = await fetch(`${apiUrl}/users/verify?` + new URLSearchParams({
                username: email,
                type: 'login',
            }));
            if (response.ok) {
                let resp = await response.json();

                toast({
                    title: resp.message,
                    status: "success",
                    duration: 3000,
                    isClosable: true,
                });
                setIsEmailSent(true);
            } else {
                throw new Error(t('failed_to_send_verification_code'));
            }
        } catch (error) {
            toast({
                title: t('failed_to_send_verification_code'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`${apiUrl}/users/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: email,
                    vcode: verificationCode,
                    mode: 'temp_token',
                }),
            });

            if (response.ok) {
                const data = await response.json();
                logon(data.data);
            } else {
                throw new Error(t('verification_failed'));
            }
        } catch (error) {
            toast({
                title: t('verification_failed'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <VStack spacing={4}>
            {!isEmailSent ? (
                <>
                    <FormControl isInvalid={!!error}>
                        <FormLabel>{t('email_address')}</FormLabel>
                        <Input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder={t('enter_your_email')}
                        />
                        <FormErrorMessage>{error}</FormErrorMessage>
                    </FormControl>

                    {/* 图片验证码 */}
                    <FormControl isInvalid={!!captchaError}>
                        <FormLabel>{t('captcha_verification')}</FormLabel>
                        <HStack spacing={3}>
                            <Input
                                value={captchaInput}
                                onChange={(e) => setCaptchaInput(e.target.value)}
                                placeholder={t('enter_captcha')}
                                maxLength={4}
                                flex={1}
                            />
                            <Box
                                cursor="pointer"
                                onClick={loadCaptcha}
                                title={t('click_to_refresh')}
                                border="1px solid"
                                borderColor="gray.200"
                                borderRadius="md"
                                p={1}
                                minW="120px"
                                h="40px"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                            >
                                {captchaUrl ? (
                                    <Image
                                        src={captchaUrl}
                                        alt="Captcha"
                                        maxH="38px"
                                        maxW="118px"
                                    />
                                ) : (
                                    <Text fontSize="sm" color="gray.500">
                                        {t('loading')}...
                                    </Text>
                                )}
                            </Box>
                        </HStack>
                        <FormErrorMessage>{captchaError}</FormErrorMessage>
                        {isCaptchaVerified && (
                            <Text fontSize="sm" color="green.500" mt={1}>
                                ✓ {t('captcha_verified')}
                            </Text>
                        )}
                    </FormControl>

                    <Button
                        onClick={handleSendVerificationCode}
                        isLoading={isLoading}
                        loadingText={t('sending')}
                        isDisabled={!email.trim() || (!isCaptchaVerified && !captchaInput.trim())}
                    >
                        {t('send_verification_code')}
                    </Button>
                </>
            ) : (
                <>
                    <HStack>
                        <PinInput onChange={(value) => setVerificationCode(value)}>
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                            <PinInputField />
                        </PinInput>
                    </HStack>
                    <Button
                        onClick={handleSubmit}
                        isLoading={isLoading}
                        isDisabled={isLoading}
                        loadingText={t('verifying')}
                    >
                        {t('verify_and_login')}
                    </Button>
                </>
            )}
        </VStack>
    );
};

export default EmailVerificationForm;
