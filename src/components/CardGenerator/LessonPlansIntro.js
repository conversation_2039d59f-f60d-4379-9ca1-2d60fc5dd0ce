import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon, Badge, Flex } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaGraduationCap, FaBrain, FaLightbulb, FaCogs, FaChartLine, FaUsers, FaClock, FaCheckCircle, FaBook, FaChalkboardTeacher, FaLaptop, FaBuilding } from 'react-icons/fa'
import { MdCheckCircle, MdSchool, MdBusiness, MdOnlinePrediction, MdAssignment } from 'react-icons/md'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="dodgerblue" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const FrameworkCard = ({ title, text, icon }) => {
    return (
        <Box
            p={4}
            bg="white"
            rounded="lg"
            shadow="sm"
            borderWidth="1px"
            borderColor="gray.200"
        >
            <Flex align="center" mb={3}>
                <Icon as={icon} w={5} h={5} color="blue.500" mr={3} />
                <Text fontWeight="bold" fontSize="md">{title}</Text>
            </Flex>
            <Text color="gray.600" fontSize="sm">{text}</Text>
        </Box>
    )
}

const LessonPlansIntro = () => {
    const { t } = useTranslation('lesson-plans')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: FaCogs,
            title: t('lesson_plans_feature_1_title'),
            text: t('lesson_plans_feature_1_text')
        },
        {
            icon: FaBrain,
            title: t('lesson_plans_feature_2_title'),
            text: t('lesson_plans_feature_2_text')
        },
        {
            icon: FaGraduationCap,
            title: t('lesson_plans_feature_3_title'),
            text: t('lesson_plans_feature_3_text')
        }
    ]

    const frameworks = [
        {
            icon: FaBook,
            title: t('framework_1_title'),
            text: t('framework_1_text')
        },
        {
            icon: FaChartLine,
            title: t('framework_2_title'),
            text: t('framework_2_text')
        },
        {
            icon: FaCogs,
            title: t('framework_3_title'),
            text: t('framework_3_text')
        },
        {
            icon: FaLightbulb,
            title: t('framework_4_title'),
            text: t('framework_4_text')
        },
        {
            icon: FaChalkboardTeacher,
            title: t('framework_5_title'),
            text: t('framework_5_text')
        },
        {
            icon: FaUsers,
            title: t('framework_6_title'),
            text: t('framework_6_text')
        }
    ]

    const useCases = [
        {
            icon: MdSchool,
            title: t('lesson_plans_use_1'),
            color: 'blue'
        },
        {
            icon: FaGraduationCap,
            title: t('lesson_plans_use_2'),
            color: 'green'
        },
        {
            icon: MdBusiness,
            title: t('lesson_plans_use_3'),
            color: 'purple'
        },
        {
            icon: MdOnlinePrediction,
            title: t('lesson_plans_use_4'),
            color: 'orange'
        }
    ]

    const benefits = [
        t('lesson_plans_benefit_1'),
        t('lesson_plans_benefit_2'),
        t('lesson_plans_benefit_3'),
        t('lesson_plans_benefit_4')
    ]

    const testimonialData = [
        {
            quote: t('testimonial_1_quote'),
            author: t('testimonial_1_author'),
            position: t('testimonial_1_position'),
            company: t('testimonial_1_company')
        },
        {
            quote: t('testimonial_2_quote'),
            author: t('testimonial_2_author'),
            position: t('testimonial_2_position'),
            company: t('testimonial_2_company')
        },
        {
            quote: t('testimonial_3_quote'),
            author: t('testimonial_3_author'),
            position: t('testimonial_3_position'),
            company: t('testimonial_3_company')
        }
    ]

    const caseStudyData = [
        {
            title: t('case_study_1_title'),
            industry: t('case_study_1_industry'),
            description: t('case_study_1_description'),
            alt: t('case_study_1_alt')
        },
        {
            title: t('case_study_2_title'),
            industry: t('case_study_2_industry'),
            description: t('case_study_2_description'),
            alt: t('case_study_2_alt')
        }
    ]

    const comparisonData = [
        {
            title: t('comparison_time_title'),
            before: t('comparison_time_before'),
            after: t('comparison_time_after')
        },
        {
            title: t('comparison_quality_title'),
            before: t('comparison_quality_before'),
            after: t('comparison_quality_after')
        },
        {
            title: t('comparison_insights_title'),
            before: t('comparison_insights_before'),
            after: t('comparison_insights_after')
        }
    ]

    const researchData = [
        {
            title: t('research_area_1_title'),
            description: t('research_area_1_description')
        },
        {
            title: t('research_area_2_title'),
            description: t('research_area_2_description')
        },
        {
            title: t('research_area_3_title'),
            description: t('research_area_3_description')
        },
        {
            title: t('research_area_4_title'),
            description: t('research_area_4_description')
        }
    ]

    return (
        <VStack spacing={0} width="100%" mt={10}>
            {/* Hero Section */}
            <Section bg="gray.50">
                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8}>
                    {coreFeatures.map((feature, index) => (
                        <Feature
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            text={feature.text}
                        />
                    ))}
                </SimpleGrid>
            </Section>

            {/* What is Educational Design */}
            <Section bg="white">
                <VStack spacing={6} textAlign="center" maxW="4xl">
                    <Heading size="lg" color="gray.800">
                        {t('why_lesson_plans_description_title')}
                    </Heading>
                    <Text fontSize="lg" color="gray.600" lineHeight="tall">
                        {t('why_lesson_plans_description')}
                    </Text>
                </VStack>
            </Section>

            {/* Benefits */}
            <Section bg="blue.50">
                <VStack spacing={8} textAlign="center">
                    <Heading size="lg" color="gray.800">
                        {t('lesson_plans_benefits_title')}
                    </Heading>
                    <List spacing={4} textAlign="left" maxW="3xl">
                        {benefits.map((benefit, index) => (
                            <ListItem key={index} display="flex" alignItems="flex-start">
                                <ListIcon as={MdCheckCircle} color="green.500" mt={1} />
                                <Text fontSize="lg">{benefit}</Text>
                            </ListItem>
                        ))}
                    </List>
                </VStack>
            </Section>

            {/* Supported Frameworks */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading size="lg" textAlign="center" color="gray.800">
                        {t('frameworks_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} width="100%">
                        {frameworks.map((framework, index) => (
                            <FrameworkCard
                                key={index}
                                icon={framework.icon}
                                title={framework.title}
                                text={framework.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Use Cases */}
            <Section bg="gray.50">
                <VStack spacing={8}>
                    <Heading size="lg" textAlign="center" color="gray.800">
                        {t('lesson_plans_uses_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} width="100%">
                        {useCases.map((useCase, index) => (
                            <Flex key={index} align="center" p={4} bg="white" rounded="lg" shadow="sm">
                                <Icon as={useCase.icon} w={6} h={6} color={`${useCase.color}.500`} mr={4} />
                                <Text fontSize="md" fontWeight="medium">{useCase.title}</Text>
                            </Flex>
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Benefits Section */}
            <Section bg="green.50">
                <VStack spacing={8} textAlign="center">
                    <Heading size="lg" color="gray.800">
                        {t('lesson_plans_transformation')}
                    </Heading>
                    <Text fontSize="lg" color="gray.600" maxW="3xl">
                        {t('comparison_description')}
                    </Text>
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%">
                        {comparisonData.map((item, index) => (
                            <VStack key={index} spacing={4} p={6} bg="white" rounded="lg" shadow="sm">
                                <Heading size="md" color="blue.600">{item.title}</Heading>
                                <Text fontSize="sm" color="gray.500">{item.before}</Text>
                                <Text fontSize="md" color="gray.700" fontWeight="medium">{item.after}</Text>
                            </VStack>
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            <HowToUse namespace="lesson-plans" />

            {/* FAQ Section */}
            <Section bg="white">
                <Heading size="lg" mb={8} textAlign="center">
                    {t('faq')}
                </Heading>
                <Accordion allowMultiple width="100%" maxW="3xl">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((i) => (
                        <AccordionItem key={i}>
                            <AccordionButton>
                                <Box flex="1" textAlign="left">
                                    <Text fontWeight="medium">{t(`lesson_plans_faq_${i}_q`)}</Text>
                                </Box>
                                <AccordionIcon />
                            </AccordionButton>
                            <AccordionPanel pb={4}>
                                <Text color="gray.600">{t(`lesson_plans_faq_${i}_a`)}</Text>
                            </AccordionPanel>
                        </AccordionItem>
                    ))}
                </Accordion>
            </Section>
        </VStack>
    )
}

export default LessonPlansIntro
