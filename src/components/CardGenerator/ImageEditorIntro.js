import { Box, Container, Heading, Text, SimpleGrid, Icon, VStack, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, useBreakpointValue, List, ListItem, ListIcon } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { FaMagic, FaImage, FaCloudUploadAlt, FaRocket, FaBrush, FaUserTie, FaInstagram, FaNewspaper, FaEdit, FaPaintBrush, FaWandMagicSparkles } from 'react-icons/fa'
import { MdCheckCircle, MdAutoFixHigh, MdImage, MdPhotoCamera, MdEdit, MdBrush, MdColorLens, MdTransform, MdEnhancedEncryption } from 'react-icons/md'
import { BiMagicWand, BiColorFill, BiCrop } from 'react-icons/bi'
import { AiOutlineEdit, AiOutlineBgColors } from 'react-icons/ai'
import Section from '../common/Section'
import HowToUse from '../common/HowToUse'

const Feature = ({ title, text, icon }) => {
    return (
        <VStack
            align={'start'}
            p={6}
            bg="white"
            rounded="xl"
            shadow="md"
            borderWidth="1px"
            spacing={4}
        >
            <Icon as={icon} w={6} h={6} color="blue.500" />
            <Text fontWeight="bold" fontSize="lg">{title}</Text>
            <Text color="gray.600">{text}</Text>
        </VStack>
    )
}

const ImageEditorIntro = () => {
    const { t } = useTranslation('image-editor')
    const isMobile = useBreakpointValue({ base: true, md: false })

    const coreFeatures = [
        {
            icon: BiMagicWand,
            title: t('editor_feature_1_title'),
            text: t('editor_feature_1_text')
        },
        {
            icon: MdEdit,
            title: t('editor_feature_2_title'),
            text: t('editor_feature_2_text')
        },
        {
            icon: MdAutoFixHigh,
            title: t('editor_feature_3_title'),
            text: t('editor_feature_3_text')
        }
    ]

    const editingCapabilities = [
        {
            icon: AiOutlineBgColors,
            title: t('editor_capability_1_title'),
            text: t('editor_capability_1_text')
        },
        {
            icon: BiCrop,
            title: t('editor_capability_2_title'),
            text: t('editor_capability_2_text')
        },
        {
            icon: MdColorLens,
            title: t('editor_capability_3_title'),
            text: t('editor_capability_3_text')
        },
        {
            icon: MdTransform,
            title: t('editor_capability_4_title'),
            text: t('editor_capability_4_text')
        },
        {
            icon: FaBrush,
            title: t('editor_capability_5_title'),
            text: t('editor_capability_5_text')
        },
        {
            icon: MdEnhancedEncryption,
            title: t('editor_capability_6_title'),
            text: t('editor_capability_6_text')
        }
    ]

    const useCases = [
        {
            icon: FaInstagram,
            title: t('editor_usecase_1_title'),
            text: t('editor_usecase_1_text')
        },
        {
            icon: FaUserTie,
            title: t('editor_usecase_2_title'),
            text: t('editor_usecase_2_text')
        },
        {
            icon: FaNewspaper,
            title: t('editor_usecase_3_title'),
            text: t('editor_usecase_3_text')
        },
        {
            icon: FaRocket,
            title: t('editor_usecase_4_title'),
            text: t('editor_usecase_4_text')
        }
    ]

    return (
        <VStack spacing={16} width="100%" alignItems="center">
            {/* Introduction */}
            <Section bg="gray.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_intro_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="4xl"
                    >
                        {t('editor_intro_description')}
                    </Text>
                </VStack>
            </Section>

            {/* Core Features */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_features_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%">
                        {coreFeatures.map((feature, index) => (
                            <Feature
                                key={index}
                                icon={feature.icon}
                                title={feature.title}
                                text={feature.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Editing Capabilities */}
            <Section bg="gray.50">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_capabilities_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} width="100%">
                        {editingCapabilities.map((capability, index) => (
                            <Feature
                                key={index}
                                icon={capability.icon}
                                title={capability.title}
                                text={capability.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Use Cases */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_usecases_title')}
                    </Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        {useCases.map((useCase, index) => (
                            <Feature
                                key={index}
                                icon={useCase.icon}
                                title={useCase.title}
                                text={useCase.text}
                            />
                        ))}
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* How It Works */}
            <Section bg="gray.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_how_it_works_title')}
                    </Heading>
                    <Text
                        fontSize={isMobile ? 'md' : 'lg'}
                        color="gray.600"
                        textAlign="center"
                        maxW="2xl"
                        mb={4}
                    >
                        {t('editor_how_it_works_description')}
                    </Text>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('editor_process_steps_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <Text fontWeight="bold">{t(`editor_process_step_${i}_title`)}</Text>
                                        <Text color="gray.600">{t(`editor_process_step_${i}_text`)}</Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>

                        <VStack align="start" spacing={4} p={6} bg="white" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('editor_examples_title')}</Heading>
                            <List spacing={3}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        <Text as="span" fontStyle="italic" color="gray.700">
                                            "{t(`editor_example_${i}`)}"
                                        </Text>
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* Benefits */}
            <Section bg="white">
                <VStack spacing={8}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_benefits_title')}
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="blue.500">{t('editor_benefits_personal_title')}</Heading>
                            <List spacing={2}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        {t(`editor_benefit_personal_${i}`)}
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>

                        <VStack align="start" spacing={4} p={6} bg="gray.50" rounded="xl" shadow="md" borderWidth="1px">
                            <Heading size="md" color="purple.500">{t('editor_benefits_professional_title')}</Heading>
                            <List spacing={2}>
                                {[1, 2, 3, 4].map((i) => (
                                    <ListItem key={i}>
                                        <ListIcon as={MdCheckCircle} color="green.500" />
                                        {t(`editor_benefit_professional_${i}`)}
                                    </ListItem>
                                ))}
                            </List>
                        </VStack>
                    </SimpleGrid>
                </VStack>
            </Section>

            {/* FAQ */}
            <Section bg="gray.50">
                <VStack spacing={6}>
                    <Heading
                        size="lg"
                        textAlign="center"
                        bgGradient="linear(to-r, blue.400, purple.400)"
                        bgClip="text"
                    >
                        {t('editor_faq_title')}
                    </Heading>
                    <Accordion allowToggle width="100%" maxW="4xl">
                        {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                            <AccordionItem key={i}>
                                <AccordionButton>
                                    <Box flex="1" textAlign="left" fontWeight="semibold">
                                        {t(`editor_faq_${i}_q`)}
                                    </Box>
                                    <AccordionIcon />
                                </AccordionButton>
                                <AccordionPanel pb={4} color="gray.600">
                                    {t(`editor_faq_${i}_a`)}
                                </AccordionPanel>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </VStack>
            </Section>
        </VStack>
    )
}

export default ImageEditorIntro
