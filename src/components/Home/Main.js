import {
    Box, SimpleGrid, Heading, Text, Button, Icon, VStack, Flex, Link, Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon, HStack,
    Center,
    Container,
    Badge,
    Grid,
    GridItem,
    useBreakpointValue,
    useColorModeValue,
    useMediaQuery,
    Spacer,
    Image,
    Input,
    Tooltip
} from '@chakra-ui/react'
import { useState, useMemo } from 'react'
import { FaChartBar, FaSitemap, FaBrain, FaDesktop, FaBalanceScale, FaTasks, FaArrowRight, FaTools, FaUserFriends, FaRocket, FaYoutube, FaMoon, FaImage, FaCamera, FaBook, FaBusinessTime, FaBullseye, FaUserTie, FaAnchor, FaPen, FaFeather, FaRegObjectGroup, FaRegCompass, FaStar, FaHeart, FaFilm, FaQuestionCircle, FaLightbulb, FaChalkboardTeacher, FaUserAstronaut, FaUserGraduate, FaPalette, FaGraduationCap, FaChartLine, FaQuoteLeft, FaSearch, FaUserAlt, FaUsers, FaRobot, FaCheck, FaLayerGroup, FaMountain, FaRegLightbulb, FaChevronRight, FaUniversity, FaBuilding, FaLaptopCode, FaArrowDown, FaArrowCircleRight, FaTextWidth, FaPager, FaNewspaper, FaPaintBrush } from 'react-icons/fa'
import { SiOpenai, SiGooglegemini, SiAnthropic } from 'react-icons/si'
import { useTranslation } from 'next-i18next'
import Header from '../Header'
import { motion } from 'framer-motion'
import getConfig from 'next/config'
import Section from '../common/Section'
import { MdAutoFixHigh, MdPsychology, MdOutlineDesignServices, MdInsights, MdAssessment, MdEdit } from 'react-icons/md'

import Footer from '../common/Footer'
import Testimonials from '../common/Testimonials'
import CaseStudies from '../common/CaseStudies'
import ComparisonTable from '../common/ComparisonTable'
import ResearchBacked from '../common/ResearchBacked'

export const aiTools = {
    Mindmap: [
        {
            category: 'Mindmap',
            icon: FaSitemap,
            title: 'AI Mindmap',
            description: 'ai_mindmap_desc',
            link: '/mindmap',
            tag: 'Mindmap Generator',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaLayerGroup,
            title: 'AI MindLadder',
            description: 'ai_layeredexplanation_desc',
            link: '/layered-explanation',
            tag: 'AI Education',
            gradient: 'linear(to-r, blue.400, purple.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaChalkboardTeacher,
            title: 'AI MarzanoBrain',
            description: 'ai_marzano_desc',
            link: '/marzano',
            tag: 'AI Education',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaChalkboardTeacher,
            title: 'AI BloomBrain',
            description: 'ai_bloom_desc',
            link: '/bloom',
            tag: 'AI Education',
            gradient: 'linear(to-r, red.400, yellow.500)', // Reassigned gradient for the last card
            bgColor: 'rgba(255, 204, 204, 0.3)', // Reassigned background color for the last card
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaChalkboardTeacher,
            title: 'AI SOLOBrain',
            description: 'ai_solo_desc',
            link: '/solo',
            tag: 'AI Education',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaChalkboardTeacher,
            title: 'AI DOKBrain',
            description: 'ai_dok_desc',
            link: '/dok',
            tag: 'AI Education',
            gradient: 'linear(to-r, cyan.400, blue.400)',
            bgColor: 'rgba(149, 235, 245, 0.2)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: MdAssessment,
            title: 'AI DOK Assessment',
            description: 'ai_dok_assessment_desc',
            link: '/dok-assessment',
            tag: 'AI Education',
            gradient: 'linear(to-r, purple.400, blue.500)',
            bgColor: 'rgba(196, 181, 253, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaUserTie,
            title: 'AI Feynman',
            description: 'ai_feynman_desc',
            link: '/feynman',
            tag: 'AI Education',
            gradient: 'linear(to-r, orange.400, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBrain,
            title: 'AI Brainstorming',
            description: 'ai_brainstorming_desc',
            link: '/brainstorming',
            tag: 'Creative Thinking',
            gradient: 'linear(to-r, orange.400, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaRegCompass,
            title: 'AI MindKit',
            description: 'ai_mindkit_desc',
            link: '/mindkit',
            tag: 'Creative Thinking',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaYoutube,
            title: 'AI Youtube Summarizer',
            description: 'ai_youtube_desc',
            link: '/youtube',
            tag: 'Mindmap Generator',
            gradient: 'linear(to-r, red.400, yellow.500)', // Reassigned gradient for the last card
            bgColor: 'rgba(255, 204, 204, 0.3)', // Reassigned background color for the last card
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBullseye,
            title: 'AI Critical Analysis',
            description: 'ai_criticalthinking_desc',
            link: '/critical-thinking',
            tag: 'Critical Thinking',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaQuestionCircle,
            title: 'AI Question Craft',
            description: 'ai_refinequestion_desc',
            link: '/refine-question',
            tag: 'Critical Thinking',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBullseye,
            title: 'AI LogicLens',
            description: 'ai_bias_desc',
            link: '/bias',
            tag: 'Critical Thinking',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: MdPsychology,
            title: 'AI Reflection',
            description: 'ai_reflection_desc',
            link: '/reflection',
            tag: 'Critical Thinking',
            gradient: 'linear(to-r, orange.500, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBalanceScale,
            title: 'AI Decision Analyzer',
            description: 'ai_decision_desc',
            link: '/decision',
            tag: 'Critical Thinking',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBullseye,
            title: 'AI OKR Assistant',
            description: 'ai_okr_desc',
            link: '/okr',
            tag: 'Business Insights',
            gradient: 'linear(to-r, orange.500, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaUserTie,
            title: 'AI Startup Mentor',
            description: 'ai_startupmentor_desc',
            link: '/startupmentor',
            tag: 'Business Insights',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaAnchor,
            title: 'AI Business Model Analyzer',
            description: 'ai_businessmodel_desc',
            link: '/businessmodel',
            tag: 'Business Insights',
            gradient: 'linear(to-r, cyan.400, blue.400)',
            bgColor: 'rgba(149, 235, 245, 0.2)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaTasks,
            title: 'AI Task Planner',
            description: 'ai_planner_desc',
            link: '/planner',
            tag: 'Business Insights',
            gradient: 'linear(to-r, yellow.400, orange.500)',
            bgColor: 'rgba(255, 236, 179, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaHeart,
            title: 'AI Counselor',
            description: 'ai_counselor_desc',
            link: '/counselor',
            tag: 'Psychological Insights',
            gradient: 'linear(to-r, cyan.400, blue.400)',
            bgColor: 'rgba(169, 235, 245, 0.2)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaMoon,
            title: 'AI DreamLens',
            description: 'ai_dreamlens_desc',
            link: '/dreamlens',
            tag: 'Psychological Insights',
            gradient: 'linear(to-r, orange.500, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaStar,
            title: 'AI Horoscope',
            description: 'ai_horoscope_desc',
            link: '/horoscope',
            tag: 'Psychological Insights',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaImage,
            title: 'AI Art Insight',
            description: 'ai_art_desc',
            link: '/art',
            tag: 'Image Insights',
            gradient: 'linear(to-r, orange.500, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaCamera,
            title: 'AI Photo Coach',
            description: 'ai_photo_desc',
            link: '/photo',
            tag: 'Image Insights',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaFeather,
            title: 'AI Poetic Lens',
            description: 'ai_poetic_desc',
            link: '/poetic',
            tag: 'Image Insights',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaBook,
            title: 'AI Reading Map',
            description: 'ai_reading_desc',
            link: '/reading',
            tag: 'Mindmap Generator',
            gradient: 'linear(to-r, cyan.400, blue.400)',
            bgColor: 'rgba(149, 235, 245, 0.2)',
            isLaunched: true
        },
        {
            category: 'Mindmap',
            icon: FaFilm,
            title: 'AI CineMap',
            description: 'ai_movie_desc',
            link: '/movie',
            tag: 'Mindmap Generator',
            gradient: 'linear(to-r, red.400, yellow.500)', // Reassigned gradient for the last card
            bgColor: 'rgba(255, 204, 204, 0.3)', // Reassigned background color for the last card
            isLaunched: true
        }
    ],

    Infographics: [
        {
            category: 'Infographics',
            icon: FaChartBar,
            title: 'AI Graphics',
            description: 'ai_graphics_desc',
            link: '/graphics',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Infographics',
            icon: FaRegObjectGroup,
            title: 'AI Infographic Generator',
            description: 'ai_infographic_desc',
            link: '/infographic',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        },
        {
            category: 'Infographics',
            icon: FaRegCompass,
            title: 'AI MindSnap',
            description: 'ai_mindsnap_desc',
            link: '/mindsnap',
            gradient: 'linear(to-r, orange.400, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Infographics',
            icon: FaRegLightbulb,
            title: 'AI InsightCards',
            description: 'ai_insightcards_desc',
            link: '/insightcards',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(210, 214, 255, 0.3)',
            isLaunched: true
        },
    ],

    Slides: [
        {
            category: 'Slides',
            icon: FaDesktop,
            title: 'AI PPT/Slides',
            description: 'ai_slides_desc',
            link: '/slides',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        },
        {
            category: 'Slides',
            icon: FaBrain,
            title: 'AI SlideGenius',
            description: 'ai_onepageslide_desc',
            link: '/one-page-slide',
            gradient: 'linear(to-r, orange.400, red.500)',
            bgColor: 'rgba(254, 235, 200, 0.3)',
            isLaunched: true
        },
        {
            category: 'Slides',
            icon: FaUserAstronaut,
            title: 'AI EduSlides',
            tag: 'AI Education',
            description: 'ai_teachingslides_desc',
            link: '/teaching-slides',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        }
    ],

    Images: [
        {
            category: 'Images',
            icon: FaPaintBrush,
            title: 'AI Sketch',
            description: 'ai_sketch_desc',
            link: '/sketch',
            tag: 'Creative Drawing',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(237, 242, 247, 0.3)',
            isLaunched: true
        },
        {
            category: 'Images',
            icon: MdEdit,
            title: 'AI Image Editor',
            description: 'ai_imageeditor_desc',
            link: '/image-editor',
            gradient: 'linear(to-r, purple.400, pink.500)',
            bgColor: 'rgba(237, 213, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Images',
            icon: FaUserAstronaut,
            title: 'AI Avatar Generator',
            description: 'ai_avatar_desc',
            link: '/avatar',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        },
        {
            category: 'Images',
            icon: MdAutoFixHigh,
            title: 'AI Watermark Remover',
            description: 'ai_erase_desc',
            link: '/erase',
            gradient: 'linear(to-r, green.400, teal.500)',
            bgColor: 'rgba(209, 250, 229, 0.3)',
            isLaunched: true
        }
    ],

    Text: [
        {
            category: 'Text',
            icon: FaUserAstronaut,
            title: 'AI Prompt Optimizer',
            description: 'ai_promptoptimizer_desc',
            link: '/prompt-optimizer',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        }, {
            category: 'Text',
            icon: FaUserAstronaut,
            title: 'AI Lesson Plans',
            description: 'ai_lessonplans_desc',
            tag: 'AI Education',
            link: '/lesson-plans',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        }, {
            category: 'Text',
            icon: FaUserAstronaut,
            title: 'AI DOK Assessment',
            description: 'ai_dokassessment_desc',
            tag: 'AI Education',
            link: '/dok-assessment',
            gradient: 'linear(to-r, cyan.400, blue.500)',
            bgColor: 'rgba(179, 245, 255, 0.3)',
            isLaunched: true
        },
    ]
}

const MotionBox = motion(Box)

const ToolCard = ({ icon, title, description, link, isLaunched, tag, category, isFeatured, isNew }) => {
    // Determine color scheme based on tag - using more subtle colors
    const getColorScheme = (tag) => {
        switch (tag) {
            case 'Mindmap Generator': return { color: 'blue', shade: '300' };
            case 'AI Education': return { color: 'teal', shade: '400' };
            case 'Creative Thinking': return { color: 'purple', shade: '500' };
            case 'Critical Thinking': return { color: 'cyan', shade: '600' };
            case 'Business Insights': return { color: 'green', shade: '600' };
            case 'Psychological Insights': return { color: 'pink', shade: '500' };
            case 'Image Insights': return { color: 'red', shade: '300' };
            default:
                if (category === 'Slides') {
                    return { color: 'blue', shade: '600' };
                } else if (category === 'Infographics') {
                    return { color: 'green', shade: '400' };
                }

                return { color: 'blue', shade: '500' };
        }
    };

    const { color, shade } = getColorScheme(tag);
    const { t } = useTranslation('common');
    const isMobile = useBreakpointValue({ base: true, md: false });

    return (
        <MotionBox
            as={Link}
            href={isLaunched && link}
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ y: -5 }}
            p={0}
            borderRadius="lg"
            boxShadow="md"
            bg="white"
            position="relative"
            overflow="hidden"
            _hover={{
                textDecoration: 'none',
                transform: 'scale(1.02)',
                boxShadow: 'lg'
            }}
            transition="all 0.3s"
            border="1px solid"
            borderColor="gray.100"
            height="100%"
            display="flex"
            flexDirection="column"
            role="group"
            aria-label={title}
            touchAction="manipulation"
        >
            {/* Colored top bar - more subtle */}
            <Box
                h="4px"
                bg={`${color}.${shade}`}
                w="100%"
                opacity={0.8}
            />

            {/* Tag badges */}
            <Flex position="absolute" top={2} right={2} flexDirection="column" alignItems="flex-end" gap={1} zIndex="1">
                {tag && (
                    <Badge
                        bg={`${color}.50`}
                        color={`${color}.${shade}`}
                        borderRadius="full"
                        px={2}
                        py={0.5}
                        fontSize={{ base: "2xs", md: "xs" }}
                        fontWeight="medium"
                    >
                        {tag}
                    </Badge>
                )}
            </Flex>

            {/* Card content */}
            <Box
                p={{ base: 4, md: 5 }}
                pt={{ base: 3, md: 4 }}
                display="flex"
                flexDirection="column"
                height="100%"
                minH={{ base: "auto", md: "180px" }}
                flex="1"
            >
                {/* Top content */}
                <VStack spacing={{ base: 2, md: 4 }} align="start" flex="1">
                    <Flex
                        width="100%"
                        direction={{ base: "row", sm: "row" }}
                        align={{ base: "center", sm: "center" }}
                        gap={{ base: 3, sm: 3 }}
                    >
                        <Box
                            p={{ base: 2, md: 2.5 }}
                            borderRadius="md"
                            bg={`${color}.50`}
                            minWidth={{ base: "36px", md: "auto" }}
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Icon
                                as={icon}
                                w={{ base: 5, md: 6 }}
                                h={{ base: 5, md: 6 }}
                                color={`${color}.${shade}`}
                            />
                        </Box>
                        <Heading
                            size={{ base: "sm", md: "md" }}
                            color="gray.700"
                            noOfLines={2}
                            lineHeight="shorter"
                        >
                            {title}
                        </Heading>
                    </Flex>

                    {/* Only show description if not featured or if on mobile */}
                    {description && (
                        <Text
                            color="gray.600"
                            fontSize={{ base: "sm", md: "md" }}
                            noOfLines={3}
                            lineHeight="1.5"
                            mt={{ base: 1, md: 2 }}
                        >
                            {description}
                        </Text>
                    )}
                </VStack>

                {/* Button at the bottom */}
                <Button
                    rightIcon={isLaunched ? <FaArrowRight /> : undefined}
                    variant="outline"
                    colorScheme={isLaunched ? color : "gray"}
                    size={{ base: "md", md: "md" }}
                    disabled={!isLaunched}
                    mt={{ base: 3, md: 4 }}
                    mb={{ base: 0, md: 0 }}
                    width="full"
                    height={{ base: "40px", md: "40px" }}
                    _hover={{
                        transform: 'translateX(4px)',
                        bg: `${color}.50`
                    }}
                    transition="all 0.2s"
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    _groupHover={{
                        bg: `${color}.50`,
                        borderColor: `${color}.400`
                    }}
                >
                    {isLaunched ? t('try_for_free', 'Try for Free Now') : t('coming_soon', 'Coming soon ...')}
                </Button>
            </Box>
        </MotionBox>
    );
}

// Benefits data
const benefits = [
    {
        id: 'deep-thinking',
        title: 'deep_thinking',
        description: 'deep_thinking_desc',
        icon: FaBrain,
        color: 'blue'
    },
    {
        id: 'boosted-creativity',
        title: 'boosted_creativity',
        description: 'boosted_creativity_desc',
        icon: FaLightbulb,
        color: 'orange'
    },
    {
        id: 'enhanced-productivity',
        title: 'enhanced_productivity',
        description: 'enhanced_productivity_desc',
        icon: FaRocket,
        color: 'green'
    }
];
// Create motion components for animations
const MotionFlex = motion(Flex);

const HeroSection = () => {
    // Theme colors
    const bgColor = useColorModeValue('gray.50', 'gray.900');
    const headingColor = useColorModeValue('gray.800', 'white');
    const subTextColor = useColorModeValue('gray.600', 'gray.300');
    const isMobile = useBreakpointValue({ base: true, md: false });
    const isSmallMobile = useBreakpointValue({ base: true, sm: false });
    const { t } = useTranslation('common');

    // Enhanced color scheme
    const colors = {
        mindMap: useColorModeValue('blue.500', 'red.400'),
        slides: useColorModeValue('cyan.500', 'purple.400'),
        infographics: useColorModeValue('green.500', 'green.400'),
        images: useColorModeValue('red.500', 'orange.400'),
        text: useColorModeValue('purple.500', 'orange.400'),

        ai: useColorModeValue('blue.500', 'blue.400'),
        highlight: useColorModeValue('teal.500', 'teal.300'),
        cardBg: useColorModeValue('white', 'gray.800'),
        cardBorder: useColorModeValue('gray.200', 'gray.700'),
    };

    // Animation variants - optimized for mobile
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.4,
                when: 'beforeChildren',
                staggerChildren: isMobile ? 0.1 : 0.2
            }
        }
    };

    const itemVariants = {
        hidden: { y: 15, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: isMobile ? 0.3 : 0.5
            }
        }
    };

    // Category data
    const categories = [
        {
            id: 'mindmap',
            title: t('category_mindmap_title'),
            icon: FaSitemap,
            color: colors.mindMap,
            link: 'Mindmap',
            description: t('category_mindmap_desc')
        },
        {
            id: 'infographics',
            title: t('category_infographics_title'),
            icon: FaChartBar,
            color: colors.infographics,
            link: 'Infographics',
            description: t('category_infographics_desc')
        },
        {
            id: 'slides',
            title: t('category_slides_title'),
            icon: FaDesktop,
            color: colors.slides,
            link: 'Slides',
            description: t('category_slides_desc')
        },
        {
            id: 'images',
            title: t('category_images_title'),
            icon: FaImage,
            color: colors.images,
            link: 'Images',
            description: t('category_images_desc')
        },
        {
            id: 'text',
            title: t('category_text_title'),
            icon: FaNewspaper,
            color: colors.text,
            link: 'Text',
            description: t('category_text_desc')
        }
    ];

    return (
        <Section bg={bgColor}>
            {/* Hero Header Section */}
            <VStack
                spacing={{ base: 4, md: 5 }}
                mb={{ base: 8, md: 12 }}
                as={motion.div}
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                maxW="4xl"
                mx="auto"
                textAlign="center"
                px={{ base: 3, md: 0 }}
                width="100%"
            >
                {/* Product Hunt Badge */}
                <Box
                    as={motion.div}
                    variants={itemVariants}
                    display="flex"
                    justifyContent="center"
                    width="100%"
                    gap={4}
                    flexWrap={{ base: "nowrap", sm: "wrap" }}
                    alignItems="center"
                >
                    <a href="https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_source=badge-funblocks&#0045;ai&#0045;brainstorming" target="_blank">
                        <img
                            src="https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=963998&theme=light&period=daily&t=1747700452719"
                            alt="FunBlocks&#0032;AI&#0032;Brainstorming - AI&#0045;Powered&#0032;Brainstorming&#0032;to&#0032;Ignite&#0032;Unlimited&#0032;Creativity | Product Hunt"
                            style={{ width: "250px", height: "54px" }}
                            width="250"
                            height="54" />
                    </a>
                    <a href="https://www.producthunt.com/posts/funblocks-ai-brainstorming?embed=true&utm_source=badge-top-post-topic-badge&utm_medium=badge&utm_source=badge-funblocks&#0045;ai&#0045;brainstorming" target="_blank">
                        <img
                            src="https://api.producthunt.com/widgets/embed-image/v1/top-post-topic-badge.svg?post_id=963998&theme=light&period=weekly&topic_id=204&t=1747700452719"
                            alt="FunBlocks&#0032;AI&#0032;Brainstorming - AI&#0045;Powered&#0032;Brainstorming&#0032;to&#0032;Ignite&#0032;Unlimited&#0032;Creativity | Product Hunt"
                            style={{ width: "250px", height: "54px" }}
                            width="250"
                            height="54"
                        />
                    </a>
                </Box>

                <Flex
                    as={motion.div}
                    variants={itemVariants}
                    mb={{ base: 1, md: 2 }}
                    direction={{ base: "column", sm: "row" }}
                    gap={{ base: 2, sm: 2 }}
                    justify="center"
                    align="center"
                    width="100%"
                    flexWrap={{ base: "nowrap", sm: "wrap" }}
                >
                    <Badge
                        colorScheme="teal"
                        fontSize={{ base: "2xs", sm: "xs", md: "sm" }}
                        px={{ base: 2, md: 3 }}
                        py={{ base: 1, md: 1 }}
                        borderRadius="full"
                        textTransform="uppercase"
                        fontWeight="medium"
                    >
                        {t('hero_badge_1')}
                    </Badge>
                    <Badge
                        colorScheme="teal"
                        fontSize={{ base: "2xs", sm: "xs", md: "sm" }}
                        px={{ base: 2, md: 3 }}
                        py={{ base: 1, md: 1 }}
                        borderRadius="full"
                        textTransform="uppercase"
                        fontWeight="medium"
                    >
                        {t('hero_badge_2')}
                    </Badge>
                </Flex>

                <Heading
                    as={motion.h1}
                    variants={itemVariants}
                    size={{ base: "lg", sm: "xl", md: "2xl" }}
                    color={headingColor}
                    lineHeight={{ base: "1.3", md: "1.2" }}
                    fontWeight="bold"
                    mb={{ base: 3, md: 4 }}
                    px={{ base: 2, md: 0 }}
                >
                    <Box as="span" color="blue.500">{t('hero_heading_1')}</Box> {t('hero_heading_2')}{' '}
                    <Box as="span" bgGradient="linear(to-r, blue.400, teal.500)" bgClip="text">
                        {t('hero_heading_3')}
                    </Box>
                </Heading>

                <Text
                    as={motion.p}
                    variants={itemVariants}
                    fontSize={{ base: "sm", sm: "md", md: "xl" }}
                    color={subTextColor}
                    maxW="3xl"
                    mb={{ base: 4, md: 6 }}
                    px={{ base: 2, md: 0 }}
                    lineHeight={{ base: "1.6", md: "1.7" }}
                >
                    {t('platform_description_1')} {t('hero_description')}
                </Text>

                <Flex
                    as={motion.div}
                    variants={itemVariants}
                    mt={{ base: 1, md: 2 }}
                    direction={{ base: "column", sm: "row" }}
                    width={{ base: "100%", sm: "auto" }}
                    justify="center"
                    align="center"
                    gap={{ base: 3, sm: 4 }}
                >
                    <Button
                        as={Link}
                        href="#ai_tools"
                        size={{ base: "md", md: "lg" }}
                        colorScheme="blue"
                        rightIcon={<FaArrowRight />}
                        _hover={{ transform: 'translateY(-2px)' }}
                        transition="all 0.3s"
                        px={{ base: 5, md: 8 }}
                        py={{ base: 6, md: 6 }}
                        borderRadius="full"
                        width={{ base: "100%", sm: "auto" }}
                        fontSize={{ base: "sm", md: "md" }}
                        fontWeight="medium"
                        height={{ base: "48px", md: "auto" }}
                    >
                        {t('explore_tools')}
                    </Button>
                    <Button
                        as={Link}
                        href="#thinking-benefits-heading"
                        size={{ base: "md", md: "lg" }}
                        variant="outline"
                        colorScheme="blue"
                        _hover={{ transform: 'translateY(-2px)' }}
                        transition="all 0.3s"
                        px={{ base: 5, md: 8 }}
                        py={{ base: 6, md: 6 }}
                        borderRadius="full"
                        width={{ base: "100%", sm: "auto" }}
                        fontSize={{ base: "sm", md: "md" }}
                        fontWeight="medium"
                        height={{ base: "48px", md: "auto" }}
                    >
                        {t('learn_more')}
                    </Button>
                </Flex>
            </VStack>

            {/* Benefits Section */}
            <SimpleGrid
                columns={{ base: 1, md: 3 }}
                spacing={{ base: 5, md: 8 }}
                mb={{ base: 10, md: 16 }}
                as={motion.div}
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                px={{ base: 3, md: 0 }}
                width="100%"
            >
                {benefits.map((benefit, idx) => (
                    <MotionBox
                        key={benefit.id}
                        variants={itemVariants}
                        bg={colors.cardBg}
                        p={{ base: 5, md: 6 }}
                        borderRadius="lg"
                        boxShadow="md"
                        border="1px solid"
                        borderColor={colors.cardBorder}
                        transition="all 0.3s"
                        _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}
                        cursor={'pointer'}
                        onClick={() => {
                            const element = document.getElementById('thinking-benefits-heading');
                            if (element) {
                                element.scrollIntoView({ behavior: 'smooth' });
                            }
                        }}
                        role="group"
                        aria-label={t(benefit.title)}
                        touchAction="manipulation"
                    >
                        <Flex
                            direction={{ base: "column", sm: "row" }}
                            align={{ base: "center", sm: "flex-start" }}
                            textAlign={{ base: "center", sm: "left" }}
                            gap={{ base: 3, md: 4 }}
                        >
                            <Center
                                bg={`${benefit.color}.100`}
                                color={`${benefit.color}.500`}
                                borderRadius="md"
                                p={{ base: 3, md: 3 }}
                                minW={{ base: "50px", md: "50px" }}
                                minH={{ base: "50px", md: "50px" }}
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                _groupHover={{
                                    bg: `${benefit.color}.200`,
                                    transform: 'scale(1.05)'
                                }}
                                transition="all 0.2s"
                            >
                                <Icon as={benefit.icon} w={{ base: 5, md: 6 }} h={{ base: 5, md: 6 }} />
                            </Center>
                            <VStack
                                align={{ base: "center", sm: "start" }}
                                spacing={{ base: 1, md: 1 }}
                            >
                                <Heading
                                    size={{ base: "sm", md: "md" }}
                                    color={headingColor}
                                    lineHeight="shorter"
                                >
                                    {t(benefit.title)}
                                </Heading>
                                <Text
                                    color={subTextColor}
                                    fontSize={{ base: "sm", md: "md" }}
                                    lineHeight="1.5"
                                >
                                    {t(benefit.description).split('\n')[0]}
                                </Text>
                            </VStack>
                        </Flex>
                    </MotionBox>
                ))}
            </SimpleGrid>

            {/* Categories Section */}
            <Heading
                as={motion.h2}
                variants={itemVariants}
                size={{ base: "md", md: "lg" }}
                textAlign="center"
                mb={{ base: 6, md: 8 }}
                color={headingColor}
                px={{ base: 3, md: 0 }}
            >
                {t('discover_categories')}
            </Heading>

            <SimpleGrid
                columns={{ base: 1, sm: 2, lg: 5 }}
                spacing={{ base: 5, md: 8 }}
                as={motion.div}
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                px={{ base: 3, md: 0 }}
                width="100%"
            >
                {categories.map((category) => (
                    <MotionBox
                        key={category.id}
                        variants={itemVariants}
                        bg={colors.cardBg}
                        p={{ base: 5, md: 6 }}
                        borderRadius="lg"
                        boxShadow="md"
                        border="1px solid"
                        borderColor={colors.cardBorder}
                        transition="all 0.3s"
                        _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}
                        onClick={() => {
                            const element = document.getElementById(category.link);
                            if (element) {
                                element.scrollIntoView({ behavior: 'smooth' });
                            }
                        }}
                        cursor={'pointer'}
                        textDecoration="none"
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        textAlign="center"
                        height="100%"
                        role="group"
                        aria-label={category.title}
                        touchAction="manipulation"
                    >
                        <Center
                            bg={`${category.color}`}
                            color="white"
                            borderRadius="full"
                            p={{ base: 3, md: 4 }}
                            mb={{ base: 3, md: 4 }}
                            minW={{ base: "60px", md: "70px" }}
                            minH={{ base: "60px", md: "70px" }}
                            _groupHover={{
                                transform: 'scale(1.05)',
                                boxShadow: 'md'
                            }}
                            transition="all 0.2s"
                        >
                            <Icon as={category.icon} w={{ base: 6, md: 7 }} h={{ base: 6, md: 7 }} />
                        </Center>
                        <Heading
                            size={{ base: "sm", md: "md" }}
                            color={headingColor}
                            mb={{ base: 2, md: 2 }}
                            lineHeight="shorter"
                        >
                            {category.title}
                        </Heading>
                        <Text
                            color={subTextColor}
                            fontSize={{ base: "xs", md: "sm" }}
                            lineHeight="1.5"
                        >
                            {category.description}
                        </Text>
                    </MotionBox>
                ))}
            </SimpleGrid>

            {/* Stats Section */}
            <SimpleGrid
                columns={{ base: 1, md: 3 }}
                spacing={{ base: 5, md: 8 }}
                mt={{ base: 10, md: 16 }}
                mb={{ base: 6, md: 8 }}
                as={motion.div}
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                px={{ base: 3, md: 0 }}
                width="100%"
            >
                {[
                    { value: "50+", label: t('stat_tools') },
                    { value: "10x", label: t('stat_thinking') },
                    { value: "24/7", label: t('stat_assistance') }
                ].map((stat, idx) => (
                    <MotionBox
                        key={idx}
                        variants={itemVariants}
                        textAlign="center"
                        bg={colors.cardBg}
                        p={{ base: 5, md: 6 }}
                        borderRadius="lg"
                        boxShadow="sm"
                        border="1px solid"
                        borderColor={colors.cardBorder}
                        transition="all 0.3s"
                        _hover={{ transform: 'translateY(-3px)', boxShadow: 'md' }}
                    >
                        <Heading
                            size={{ base: "xl", md: "2xl" }}
                            bgGradient="linear(to-r, blue.400, teal.500)"
                            bgClip="text"
                            mb={{ base: 1, md: 2 }}
                        >
                            {stat.value}
                        </Heading>
                        <Text
                            color={subTextColor}
                            fontSize={{ base: "md", md: "lg" }}
                            fontWeight="medium"
                        >
                            {stat.label}
                        </Text>
                    </MotionBox>
                ))}
            </SimpleGrid>
        </Section>
    );
};

const ThinkingSection = () => {
    const { t } = useTranslation('common')
    const cardBg = useColorModeValue('white', 'gray.700')

    return (
        <Section id="thinking-benefits" bg='blue.50'>
            <VStack spacing={12} width="100%">
                <Heading id='thinking-benefits-heading' color={'dodgerblue'} textAlign="center" size="xl">
                    {t('thinking_benefits_title')}
                </Heading>

                <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%">
                    {benefits.map((card) => (
                        <MotionBox
                            key={card.id}
                            bg={cardBg}
                            p={8}
                            // pt={4}
                            borderRadius="xl"
                            boxShadow="xl"
                            backgroundColor={'#fafafa'}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.5 }}
                        >
                            <VStack spacing={4} align="flex-start">
                                {/* <Icon
                                    as={card.icon}
                                    w={10}
                                    h={10}
                                    bgGradient={gradient}
                                    bgClip="text"
                                />
                                <Heading size="md">{card.title}</Heading> */}
                                <HStack>
                                    {/* <Icon
                                        as={card.icon}
                                        w={7}
                                        h={7}
                                        style={{
                                            color: card.color,
                                        }}
                                        // bgGradient={gradient}
                                        bgClip="text"
                                    /> */}
                                    <Center
                                        bg={`${card.color}.100`}
                                        color={`${card.color}.500`}
                                        borderRadius="md"
                                        p={3}
                                    >
                                        <Icon as={card.icon} w={6} h={6} />
                                    </Center>
                                    <Heading size="md" color={`${card.color}.500`}>{t(card.title)}</Heading>
                                </HStack>
                                <ul>
                                    {t(card.description).split("\n").map((text, index) => (
                                        <li key={index}>
                                            <Text color="gray.600" fontSize="lg">{text}</Text>
                                        </li>
                                    ))}
                                </ul>
                            </VStack>
                        </MotionBox>
                    ))}
                </SimpleGrid>
            </VStack>
        </Section>
    )
}

const AIToolsSection = ({ isMobile, basePath }) => {
    const { t } = useTranslation('common');
    const [searchQuery, setSearchQuery] = useState('');
    const [activeCategory, setActiveCategory] = useState('all');
    const [activeCategories, setActiveCategories] = useState([]);

    // Color scheme for categories - more subtle colors
    const categoryColors = {
        // Main categories
        'Mindmap': 'blue',
        'Infographics': 'green',
        'Slides': 'cyan',
        'Images': 'red',
        'Text': 'purple',
        // Sub-categories (tags)
        'Mindmap Generator': 'blue',
        'AI Education': 'teal',
        'Creative Thinking': 'purple',
        'Critical Thinking': 'cyan',
        'Business Insights': 'green',
        'Psychological Insights': 'pink',
        'Image Insights': 'red',
        'all': 'gray'
    };

    // Get all unique tags across all tool categories
    const allTags = useMemo(() => {
        const tags = new Set();
        Object.values(aiTools).forEach(toolCategory => {
            toolCategory.forEach(tool => {
                if (tool.tag) tags.add(tool.tag);
            });
        });
        return ['all', ...Array.from(tags)];
    }, []);

    // Filter tools based on search query and active category
    const filteredTools = useMemo(() => {
        // Get tools from all selected categories, or all categories if none selected
        let tools = [];

        if (activeCategories.length === 0) {
            // If no categories selected, show all tools with their category info
            Object.keys(aiTools).forEach(category => {
                // Make sure each tool has its main category property
                const toolsWithCategory = aiTools[category].map(tool => ({
                    ...tool,
                    mainCategory: category // Add main category for grouping
                }));
                tools = [...tools, ...toolsWithCategory];
            });
        } else {
            // Get tools from selected categories
            activeCategories.forEach(category => {
                if (aiTools[category]) {
                    // Add the category to each tool for reference
                    const toolsWithCategory = aiTools[category].map(tool => ({
                        ...tool,
                        mainCategory: category
                    }));
                    tools = [...tools, ...toolsWithCategory];
                }
            });
        }

        return tools.filter(tool => {
            const matchesSearch = searchQuery === '' ||
                tool.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                t(tool.description).toLowerCase().includes(searchQuery.toLowerCase());

            const matchesCategory = activeCategory === 'all' || tool.tag === activeCategory;

            return matchesSearch && matchesCategory;
        });
    }, [activeCategories, searchQuery, activeCategory, t]);

    // Group filtered tools by tag or by main category when no category is selected
    const groupedFilteredTools = useMemo(() => {
        if (activeCategories.length === 0) {
            // When no category is selected, group by main category first, then by tag
            const groupedByMainCategory = {};

            // First group by main category
            filteredTools.forEach(tool => {
                const mainCategory = tool.mainCategory || 'Other';
                if (!groupedByMainCategory[mainCategory]) {
                    groupedByMainCategory[mainCategory] = [];
                }
                groupedByMainCategory[mainCategory].push(tool);
            });

            // Then within each main category, group by tag
            const result = {};
            Object.keys(groupedByMainCategory).forEach(mainCategory => {
                const categoryKey = `${mainCategory}`;
                result[categoryKey] = groupedByMainCategory[mainCategory];
            });

            return result;
        } else {
            // When a category is selected, group by tag as before
            return filteredTools.reduce((acc, tool) => {
                const tag = tool.tag || 'Uncategorized';
                if (!acc[tag]) {
                    acc[tag] = [];
                }
                acc[tag].push(tool);
                return acc;
            }, {});
        }
    }, [filteredTools, activeCategories]);

    return (
        <Section id="ai_tools" pt={{ base: 8, md: 12 }}>
            <Box px={{ base: 4, md: 0 }}>
                <Heading
                    id='ai_tools'
                    size={{ base: "lg", md: "xl" }}
                    bgGradient="linear(to-r, blue.400, fuchsia)"
                    bgClip="text"
                    mb={{ base: 4, md: 6 }}
                    mt={0}
                    lineHeight={1.2}
                    textAlign="center"
                >
                    {t('ai_tools_heading')}
                </Heading>

                <Text
                    fontSize={{ base: "md", md: "xl" }}
                    color="gray.600"
                    maxW="3xl"
                    mx="auto"
                    textAlign="center"
                    mb={{ base: 6, md: 8 }}
                    px={{ base: 2, md: 0 }}
                >
                    {t('ai_tools_description')}
                </Text>
            </Box>

            {/* Featured Sections - Only show when no filters are applied */}

            {/* Popular Tools */}
            <Box mb={{ base: 8, md: 10 }} width={'100%'} px={{ base: 4, md: 0 }}>
                <Flex
                    align="center"
                    mb={{ base: 4, md: 6 }}
                    flexWrap="nowrap"
                >
                    <Heading
                        size={{ base: "sm", md: "md" }}
                        color="gray.700"
                        whiteSpace="nowrap"
                    >
                        <Icon as={FaStar} color="yellow.500" mr={2} />
                        {t('popular_tools', 'Popular Tools')}
                    </Heading>
                    <Box flex="1" height="1px" bg="gray.200" ml={4} />
                </Flex>

                <SimpleGrid
                    columns={{ base: 1, sm: 2, lg: 4 }}
                    spacing={{ base: 4, md: 6 }}
                >
                    {/* Hardcoded selection of popular tools from different categories */}
                    {[
                        aiTools.Mindmap.find(tool => tool.title === 'AI Mindmap'),
                        aiTools.Infographics.find(tool => tool.title === 'AI Infographic Generator'),
                        aiTools.Slides.find(tool => tool.title === 'AI PPT/Slides'),
                        aiTools.Mindmap.find(tool => tool.title === 'AI Brainstorming')
                    ].map((tool, index) => tool && (
                        <ToolCard
                            key={index}
                            {...tool}
                            description={t(tool.description)}
                            link={basePath + tool.link}
                            tag={tool.tag}
                            isFeatured={true} // Add flag to indicate this is a featured card
                            isLaunched={true}
                        />
                    ))}
                </SimpleGrid>
            </Box>

            {/* What's New Section */}
            <Box mb={{ base: 8, md: 10 }} width={'100%'} px={{ base: 4, md: 0 }}>
                <Flex
                    align="center"
                    mb={{ base: 4, md: 6 }}
                    flexWrap="nowrap"
                >
                    <Heading
                        size={{ base: "sm", md: "md" }}
                        color="gray.700"
                        whiteSpace="nowrap"
                        display="flex"
                        alignItems="center"
                    >
                        <Icon as={FaRocket} color="purple.500" mr={2} />
                        {t('whats_new', "What's New")}
                        <Badge ml={2} colorScheme="purple" variant="solid" fontSize={{ base: "2xs", md: "xs" }}>NEW</Badge>
                    </Heading>
                    <Box flex="1" height="1px" bg="gray.200" ml={4} />
                </Flex>

                <SimpleGrid
                    columns={{ base: 1, sm: 2, lg: 4 }}
                    spacing={{ base: 4, md: 6 }}
                >
                    {/* Hardcoded selection of newest tools */}
                    {[
                        aiTools.Mindmap.find(tool => tool.title === 'AI MindKit'),
                        aiTools.Infographics.find(tool => tool.title === 'AI InsightCards'),
                        aiTools.Mindmap.find(tool => tool.title === 'AI LogicLens'),
                        aiTools.Images.find(tool => tool.title === 'AI Avatar Generator')
                    ].map((tool, index) => tool && (
                        <ToolCard
                            key={index}
                            {...tool}
                            description={t(tool.description)}
                            link={basePath + tool.link}
                            tag={tool.tag}
                            isFeatured={true} // Add flag to indicate this is a featured card
                            isNew={true} // Add flag to indicate this is a new card
                            isLaunched={true}
                        />
                    ))}
                </SimpleGrid>
            </Box>



            <Box mb={{ base: 8, md: 10 }} width={'100%'} px={{ base: 4, md: 0 }}>
                <Flex
                    align="center"
                    mb={{ base: 8, md: 12 }}
                    mt={{ base: 4, md: 6 }}
                    flexWrap="nowrap"
                >
                    <Box flex="1" height="1px" bg="gray.200" mr={4} />
                    <Heading size={{ base: "sm", md: "md" }} color="gray.700" whiteSpace="nowrap">
                        {t('more', "More")}
                    </Heading>
                    <Box flex="1" height="1px" bg="gray.200" ml={4} />
                </Flex>

                {/* Main category tabs */}
                <Flex
                    justify="center"
                    mb={{ base: 6, md: 8 }}
                    overflowX="auto"
                    pb={{ base: 2, md: 0 }}
                    mx={{ base: -4, md: 0 }}
                    px={{ base: 4, md: 0 }}
                    css={{
                        '-webkit-overflow-scrolling': 'touch',
                        scrollbarWidth: 'thin',
                        '&::-webkit-scrollbar': {
                            height: '4px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'rgba(0,0,0,0.2)',
                            borderRadius: '4px',
                        }
                    }}

                    style={{
                        justifyContent: 'unset'
                    }}
                >
                    <HStack
                        spacing={{ base: 2, md: 4 }}
                        flexWrap={{ base: "nowrap", md: "wrap" }}
                        justify="center"
                        width={{ base: "max-content", md: "100%" }}
                    >
                        {Object.keys(aiTools).map(category => {
                            // Define category-specific colors and icons
                            const getCategoryColor = (cat) => {
                                switch (cat) {
                                    case 'Mindmap': return 'blue';
                                    case 'Infographics': return 'green';
                                    case 'Slides': return 'cyan';
                                    case 'Images': return 'red';
                                    case 'Text': return 'purple';
                                    default: return 'blue';
                                }
                            };

                            const categoryColor = getCategoryColor(category);
                            const categoryIcon =
                                category === 'Mindmap' ? FaSitemap :
                                    category === 'Infographics' ? FaChartBar :
                                        category === 'Slides' ? FaDesktop :
                                            category === 'Images' ? FaImage : 
                                                category === 'Text' ? FaNewspaper : FaTools;

                            return (
                                <Button
                                    key={category}
                                    size={{ base: "sm", md: "md" }}
                                    colorScheme={categoryColor}
                                    variant={activeCategories.includes(category) ? "solid" : "outline"}
                                    onClick={() => {
                                        // Toggle category selection (single selection only)
                                        if (activeCategories.includes(category)) {
                                            // Remove category if already selected (deselect)
                                            setActiveCategories([]);
                                        } else {
                                            // Select only this category
                                            setActiveCategories([category]);
                                        }
                                        setActiveCategory('all');
                                        setSearchQuery('');
                                    }}
                                    minW={{ base: "auto", md: "120px" }}
                                    flexShrink={0}
                                    leftIcon={<Icon as={categoryIcon} w={{ base: 4, md: 5 }} h={{ base: 4, md: 5 }} />}
                                    position="relative"
                                    px={{ base: 3, md: 4 }}
                                    _after={activeCategories.includes(category) ? {
                                        content: '""',
                                        position: 'absolute',
                                        bottom: '-4px',
                                        left: '50%',
                                        transform: 'translateX(-50%)',
                                        width: { base: '40px', md: '68px' },
                                        height: '3px',
                                        borderRadius: 'full',
                                        bg: `${categoryColor}.500`,
                                    } : {}}
                                >
                                    {category}
                                </Button>
                            );
                        })}
                    </HStack>
                </Flex>

                {/* Search and filter controls */}
                <Flex
                    direction={{ base: "column", md: "row" }}
                    justify="space-between"
                    align={{ base: "stretch", md: "center" }}
                    mb={{ base: 6, md: 8 }}
                    gap={{ base: 3, md: 4 }}
                >
                    <Box
                        position="relative"
                        w={{ base: "full", md: "300px" }}
                    >
                        <Input
                            placeholder={t('search_tools', 'Search tools...')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            pr="40px"
                            bg="white"
                            borderColor="gray.300"
                            _hover={{ borderColor: "blue.300" }}
                            _focus={{ borderColor: "blue.500", boxShadow: "0 0 0 1px blue.500" }}
                            size={{ base: "md", md: "md" }}
                            fontSize={{ base: "sm", md: "md" }}
                        />
                        <Icon
                            as={FaSearch}
                            position="absolute"
                            right="12px"
                            top="50%"
                            transform="translateY(-50%)"
                            color="gray.400"
                            w={{ base: 4, md: 5 }}
                            h={{ base: 4, md: 5 }}
                        />
                    </Box>

                    <Box
                        overflowX="auto"
                        w="full"
                        mx={{ base: -4, md: 0 }}
                        px={{ base: 4, md: 0 }}
                        pb={{ base: 2, md: 0 }}
                        css={{
                            '-webkit-overflow-scrolling': 'touch',
                            scrollbarWidth: 'thin',
                            '&::-webkit-scrollbar': {
                                height: '4px',
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'rgba(0,0,0,0.2)',
                                borderRadius: '4px',
                            }
                        }}
                    >
                        <HStack
                            spacing={{ base: 1.5, md: 2 }}
                            flexWrap={{ base: "nowrap", md: "wrap" }}
                            width={{ base: "max-content", md: "100%" }}
                            pt={1}
                        >
                            {allTags.map(tag => {
                                const isActive = activeCategory === tag;
                                const tagColor = categoryColors[tag] || 'blue';

                                return (
                                    <Box
                                        key={tag}
                                        as="button"
                                        px={{ base: 2, md: 3 }}
                                        py={{ base: 1.5, md: 2 }}
                                        borderRadius="full"
                                        bg={isActive ? `${tagColor}.500` : 'transparent'}
                                        color={isActive ? 'white' : `${tagColor}.600`}
                                        border="1px solid"
                                        borderColor={isActive ? `${tagColor}.500` : 'gray.200'}
                                        cursor="pointer"
                                        onClick={() => setActiveCategory(tag)}
                                        fontSize={{ base: "xs", md: "sm" }}
                                        fontWeight="medium"
                                        flexShrink={0}
                                        _hover={{
                                            bg: isActive ? `${tagColor}.600` : `${tagColor}.50`,
                                            transform: 'translateY(-2px)',
                                            boxShadow: 'sm'
                                        }}
                                        transition="all 0.2s"
                                        boxShadow={isActive ? 'md' : 'none'}
                                        minH={{ base: "28px", md: "32px" }}
                                        display="flex"
                                        alignItems="center"
                                        justifyContent="center"
                                    >
                                        {tag === 'all' ? t('all_categories', 'All Categories') : tag}
                                    </Box>
                                );
                            })}
                        </HStack>
                    </Box>
                </Flex>

                {/* Results count */}
                <Text
                    color="gray.500"
                    mb={{ base: 4, md: 6 }}
                    fontSize={{ base: "xs", md: "sm" }}
                >
                    {filteredTools.length === 0
                        ? t('no_tools_found', 'No tools found')
                        : `${t('showing_tools', 'Showing')} ${filteredTools.length} ${filteredTools.length === 1 ? t('tool', 'tool') : t('tools', 'tools')}`}
                </Text>


                {/* Tool cards */}
                <VStack spacing={{ base: 8, md: 10 }} align="stretch">
                    {Object.keys(groupedFilteredTools).length > 0 ? (
                        Object.keys(groupedFilteredTools).map((tag) => (
                            <Box key={tag}>
                                <Flex
                                    align="center"
                                    mb={{ base: 4, md: 6 }}
                                    id={tag}
                                    flexWrap="nowrap"
                                >
                                    {tag !== "Uncategorized" && (
                                        <>
                                            <Box
                                                p={{ base: 1.5, md: 2 }}
                                                borderRadius="md"
                                                bg={`${categoryColors[tag] || 'blue'}.50`}
                                                mr={{ base: 2, md: 3 }}
                                                flexShrink={0}
                                            >
                                                <Icon
                                                    as={
                                                        // Check if tag is a main category
                                                        tag === 'Mindmap' ? FaSitemap :
                                                            tag === 'Infographics' ? FaChartBar :
                                                                tag === 'Slides' ? FaDesktop :
                                                                    tag === 'Images' ? FaImage :
                                                                        tag === 'Text' ? FaNewspaper :
                                                                            // Otherwise use tag icons
                                                                            tag === 'Mindmap Generator' ? FaSitemap :
                                                                                tag === 'AI Education' ? FaGraduationCap :
                                                                                    tag === 'Creative Thinking' ? FaLightbulb :
                                                                                        tag === 'Critical Thinking' ? FaBullseye :
                                                                                            tag === 'Business Insights' ? FaChartLine :
                                                                                                tag === 'Psychological Insights' ? FaHeart :
                                                                                                    tag === 'Image Insights' ? FaImage :
                                                                                                        FaTools
                                                    }
                                                    color={`${categoryColors[tag] || 'blue'}.600`}
                                                    w={{ base: 4, md: 5 }}
                                                    h={{ base: 4, md: 5 }}
                                                />
                                            </Box>

                                            <Heading
                                                size={{ base: "sm", md: "md" }}
                                                color="gray.700"
                                                fontWeight="600"
                                                noOfLines={1}
                                                flexShrink={0}
                                            >
                                                {tag}
                                            </Heading>

                                            <Box
                                                ml={{ base: 2, md: 3 }}
                                                px={{ base: 1.5, md: 2 }}
                                                py={{ base: 0.5, md: 1 }}
                                                borderRadius="md"
                                                bg={`${categoryColors[tag] || 'blue'}.50`}
                                                color={`${categoryColors[tag] || 'blue'}.700`}
                                                fontSize={{ base: "xs", md: "sm" }}
                                                fontWeight="medium"
                                                flexShrink={0}
                                                minW={{ base: "40px", md: "auto" }}
                                                textAlign="center"
                                            >
                                                {groupedFilteredTools[tag].length} {groupedFilteredTools[tag].length === 1 ? 'tool' : 'tools'}
                                            </Box>
                                        </>
                                    )}

                                    <Box flex="1" height="1px" bg="gray.200" ml={{ base: 2, md: 4 }} />
                                </Flex>

                                <SimpleGrid
                                    columns={{ base: 1, sm: 2, lg: 3 }}
                                    spacing={{ base: 4, md: 8 }}
                                    w="full"
                                >
                                    {groupedFilteredTools[tag].map((tool, index) => (
                                        <ToolCard
                                            key={index}
                                            {...tool}
                                            description={t(tool.description)}
                                            link={basePath + tool.link}
                                            tag={tool.tag}
                                        />
                                    ))}
                                </SimpleGrid>
                            </Box>
                        ))
                    ) : (
                        <Box
                            textAlign="center"
                            py={{ base: 8, md: 10 }}
                            px={{ base: 4, md: 6 }}
                            bg="gray.50"
                            borderRadius="lg"
                        >
                            <Icon
                                as={FaSearch}
                                w={{ base: 8, md: 10 }}
                                h={{ base: 8, md: 10 }}
                                color="gray.400"
                                mb={{ base: 3, md: 4 }}
                            />
                            <Heading
                                size={{ base: "sm", md: "md" }}
                                color="gray.500"
                                mb={{ base: 1, md: 2 }}
                            >
                                {t('no_tools_found', 'No tools found')}
                            </Heading>
                            <Text
                                color="gray.500"
                                fontSize={{ base: "sm", md: "md" }}
                            >
                                {t('try_different_search', 'Try a different search term or category')}
                            </Text>
                            <Button
                                mt={{ base: 4, md: 6 }}
                                colorScheme="blue"
                                size={{ base: "sm", md: "md" }}
                                onClick={() => {
                                    setSearchQuery('');
                                    setActiveCategory('all');
                                    setActiveCategories([]);
                                }}
                            >
                                {t('clear_filters', 'Clear Filters')}
                            </Button>
                        </Box>
                    )}
                </VStack>
            </Box>

            {/* Quick navigation */}
            {!isMobile && filteredTools.length > 6 && (
                <Box
                    position="fixed"
                    right="20px"
                    top="50%"
                    transform="translateY(-50%)"
                    zIndex={10}
                    display={{ base: 'none', xl: 'block' }}
                >
                    <VStack
                        spacing={2}
                        bg="white"
                        p={3}
                        borderRadius="lg"
                        boxShadow="lg"
                    >
                        {Object.keys(groupedFilteredTools).map(tag => (
                            <Tooltip
                                key={tag}
                                label={tag}
                                placement="left"
                                hasArrow
                                bg={`${categoryColors[tag] || 'blue'}.500`}
                            >
                                <Link
                                    href={`#${tag}`}
                                    _hover={{ textDecoration: 'none' }}
                                >
                                    <Box
                                        p={2}
                                        borderRadius="md"
                                        bg={`${categoryColors[tag] || 'blue'}.50`}
                                        _hover={{ bg: `${categoryColors[tag] || 'blue'}.100` }}
                                        transition="all 0.2s"
                                    >
                                        <Icon
                                            as={
                                                // Check if tag is a main category
                                                tag === 'Mindmap' ? FaSitemap :
                                                    tag === 'Infographics' ? FaChartBar :
                                                        tag === 'Slides' ? FaDesktop :
                                                            tag === 'Images' ? FaImage :
                                                                tag === 'Text' ? FaNewspaper :
                                                                    // Otherwise use tag icons
                                                                    tag === 'Mindmap Generator' ? FaSitemap :
                                                                        tag === 'AI Education' ? FaGraduationCap :
                                                                            tag === 'Creative Thinking' ? FaLightbulb :
                                                                                tag === 'Critical Thinking' ? FaBullseye :
                                                                                    tag === 'Business Insights' ? FaChartLine :
                                                                                        tag === 'Psychological Insights' ? FaHeart :
                                                                                            tag === 'Image Insights' ? FaImage :
                                                                                                FaTools
                                            }
                                            color={`${categoryColors[tag] || 'blue'}.500`}
                                        />
                                    </Box>
                                </Link>
                            </Tooltip>
                        ))}
                    </VStack>
                </Box>
            )}
        </Section>
    );
}

const AIFlowSection = () => {
    const { t } = useTranslation('common');
    const isMobile = useBreakpointValue({ base: true, md: false });

    return (
        <Section bg="blue.50" id="aiflow-section">
            <VStack spacing={8} width="100%">
                <Heading
                    size="xl"
                    bgGradient="linear(to-r, blue.400, purple.500)"
                    bgClip="text"
                    textAlign="center"
                    mb={4}
                >
                    {t("aiflow_and_aitools_title")}
                </Heading>

                <Text
                    fontSize={{ base: "md", md: "lg" }}
                    color="gray.700"
                    maxW="3xl"
                    textAlign="center"
                    mb={6}
                >
                    {t("continue_exploring_ai_content")}
                </Text>

                {/* Main content grid */}
                <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={8} width="100%" maxW="7xl">
                    {/* Left column - AITools */}
                    <Box
                        p={6}
                        bg="purple.50"
                        borderRadius="xl"
                        boxShadow="lg"
                        position="relative"
                        overflow="hidden"
                        height="100%"
                        borderTop="4px solid"
                        borderColor="purple.500"
                    >
                        <VStack spacing={5} align="start" height="100%">
                            <HStack spacing={4}>
                                <Center
                                    bg="purple.100"
                                    p={2}
                                    borderRadius="md"
                                >
                                    <Icon as={FaTools} w={6} h={6} color="purple.500" />
                                </Center>
                                <Heading size="md" color="purple.600">{t('about_aitools_title')}</Heading>
                            </HStack>

                            <VStack spacing={4} align="start" width="100%">
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aitools_point1')}</Text>
                                </HStack>
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aitools_point2')}</Text>
                                </HStack>
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aitools_point3')}</Text>
                                </HStack>
                            </VStack>
                        </VStack>
                    </Box>

                    {/* Middle column - Journey visualization */}
                    <Box
                        p={6}
                        bg="white"
                        borderRadius="xl"
                        boxShadow="lg"
                        position="relative"
                        overflow="hidden"
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                    >
                        <VStack spacing={4} width="100%">
                            <HStack spacing={4} justifyContent="center" width="100%">
                                <Icon as={FaLightbulb} w={6} h={6} color="blue.500" />
                                <Heading size="md" color="blue.600">{t('what_after_generation')}</Heading>
                            </HStack>

                            <Box width="100%" position="relative" py={4}>
                                {/* Top box - AI Tools */}
                                <Box
                                    p={4}
                                    bg="purple.50"
                                    borderRadius="lg"
                                    boxShadow="md"
                                    mb={10}
                                    position="relative"
                                    zIndex={1}
                                >
                                    <HStack>
                                        <Icon as={FaTools} w={5} h={5} color="purple.500" />
                                        <Text fontWeight="bold" color="purple.600">AI Tools</Text>
                                    </HStack>
                                    <Text fontSize="sm" color="gray.600" mt={1}>
                                        {t('ai_tools_generation_explanation')}
                                    </Text>
                                </Box>

                                {/* Connecting arrow */}
                                <Box
                                    position="absolute"
                                    top="30%"
                                    left="50%"
                                    transform="translateX(-50%)"
                                    width="2px"
                                    height="40%"
                                    bg="blue.400"
                                    zIndex={0}
                                />

                                <Center
                                    position="absolute"
                                    top="50%"
                                    left="50%"
                                    transform="translate(-50%, -50%)"
                                    bg="white"
                                    borderRadius="full"
                                    boxShadow="md"
                                    p={2}
                                    zIndex={1}
                                >
                                    <Icon as={FaArrowDown} w={5} h={5} color="blue.500" />
                                </Center>

                                {/* Bottom box - AIFlow */}
                                <Box
                                    p={4}
                                    bg="blue.50"
                                    borderRadius="lg"
                                    boxShadow="md"
                                    position="relative"
                                    zIndex={1}
                                >
                                    <HStack>
                                        <Icon as={FaRobot} w={5} h={5} color="blue.500" />
                                        <Text fontWeight="bold" color="blue.600">AIFlow</Text>
                                    </HStack>
                                    <Text fontSize="sm" color="gray.600" mt={1}>
                                        {t('continue_with_aiflow')}
                                    </Text>
                                </Box>
                            </Box>

                            <Link
                                href="https://www.funblocks.net/aiflow"
                                isExternal
                                mt={2}
                                width="100%"
                            >
                                <Button
                                    colorScheme="blue"
                                    size="md"
                                    width="100%"
                                    rightIcon={<FaArrowCircleRight />}
                                    _hover={{
                                        transform: 'translateY(-2px)',
                                        boxShadow: 'md'
                                    }}
                                    transition="all 0.3s"
                                >
                                    {t("try_aiflow_now")}
                                </Button>
                            </Link>
                        </VStack>
                    </Box>

                    {/* Right column - AIFlow */}
                    <Box
                        p={6}
                        bg="blue.50"
                        borderRadius="xl"
                        boxShadow="lg"
                        position="relative"
                        overflow="hidden"
                        height="100%"
                        borderTop="4px solid"
                        borderColor="blue.500"
                    >
                        <VStack spacing={5} align="start" height="100%">
                            <HStack spacing={4}>
                                <Center
                                    bg="blue.100"
                                    p={2}
                                    borderRadius="md"
                                >
                                    <Icon as={FaRobot} w={6} h={6} color="blue.500" />
                                </Center>
                                <Heading size="md" color="blue.600">{t('about_aiflow_title')}</Heading>
                            </HStack>

                            <VStack spacing={4} align="start" width="100%">
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aiflow_point1')}</Text>
                                </HStack>
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aiflow_point2')}</Text>
                                </HStack>
                                <HStack spacing={3} width="100%">
                                    <Icon as={FaCheck} w={5} h={5} color="green.500" flexShrink={0} />
                                    <Text fontSize="md" color="gray.700">{t('about_aiflow_point3')}</Text>
                                </HStack>
                            </VStack>
                        </VStack>
                    </Box>
                </SimpleGrid>

                {/* Bottom section with image and steps */}
                <Box
                    maxW="5xl"
                    p={{ base: 4, md: 8 }}
                    bg="white"
                    borderRadius="xl"
                    boxShadow="lg"
                    mt={6}
                    width="100%"
                >
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                        <VStack spacing={5} align="start">
                            <Heading size="md" color="teal.600">
                                {t('deep_dive_capabilities')}
                            </Heading>

                            <VStack spacing={4} align="start" width="100%">
                                <HStack spacing={3} width="100%">
                                    <Center
                                        bg="teal.100"
                                        borderRadius="full"
                                        w={8}
                                        h={8}
                                        flexShrink={0}
                                    >
                                        <Text fontWeight="bold" color="teal.700">1</Text>
                                    </Center>
                                    <Text fontSize="md" color="gray.700">{t('click_continue_to_explore')}</Text>
                                </HStack>

                                <HStack spacing={3} width="100%">
                                    <Center
                                        bg="teal.100"
                                        borderRadius="full"
                                        w={8}
                                        h={8}
                                        flexShrink={0}
                                    >
                                        <Text fontWeight="bold" color="teal.700">2</Text>
                                    </Center>
                                    <Text fontSize="md" color="gray.700">{t('access_funblocks_aiflow')}</Text>
                                </HStack>

                                <HStack spacing={3} width="100%">
                                    <Center
                                        bg="teal.100"
                                        borderRadius="full"
                                        w={8}
                                        h={8}
                                        flexShrink={0}
                                    >
                                        <Text fontWeight="bold" color="teal.700">3</Text>
                                    </Center>
                                    <Text fontSize="md" color="gray.700">{t('aitools_not_covered')}</Text>
                                </HStack>
                            </VStack>

                            <Box
                                p={4}
                                bg="blue.50"
                                borderRadius="md"
                                width="100%"
                                mt={2}
                            >
                                <Text fontSize="sm" fontStyle="italic" color="blue.700">
                                    {t("aitools_not_covered")} {t("continue_with_aiflow")}
                                </Text>
                            </Box>
                        </VStack>

                        <Box>
                            <Image
                                src={"https://www.funblocks.net/img/portfolio/thumbnails/aitools_mindmap_book_generated.png"}
                                alt="AIFlow Exploration Interface"
                                borderRadius="md"
                                boxShadow="md"
                                width="100%"
                                height="auto"
                                objectFit="cover"
                            />
                            <Text fontSize="sm" color="gray.500" mt={2} textAlign="center">
                                {t('access_funblocks_aiflow')}
                            </Text>
                        </Box>
                    </SimpleGrid>
                </Box>
            </VStack>
        </Section>
    );
}

const MainFrame = () => {
    const { t } = useTranslation('common')
    const [isMobile] = useMediaQuery("(max-width: 768px)")
    const { basePath } = getConfig().publicRuntimeConfig;

    const features = [
        {
            icon: FaBrain,
            title: t('ai_powered_intelligence'),
            description: t('ai_powered_intelligence_desc'),
            gradient: 'linear(to-r, blue.400, purple.500)'
        },
        {
            icon: FaRocket,
            title: t('mental_models_toolify'),
            description: t('mental_models_toolify_desc'),
            gradient: 'linear(to-r, purple.400, pink.500)'
        },
        {
            icon: FaTools,
            title: t('integrated_toolset'),
            description: t('integrated_toolset_desc'),
            gradient: 'linear(to-r, green.400, teal.500)'
        },
        {
            icon: FaUserFriends,
            title: t('user_friendly'),
            description: t('user_friendly_desc'),
            gradient: 'linear(to-r, orange.400, red.500)'
        },
    ];



    const faqs = [
        {
            question: t('platform_faq_1_q'),
            answer: t('platform_faq_1_a')
        },
        {
            question: t('platform_faq_2_q'),
            answer: t('platform_faq_2_a')
        },
        {
            question: t('platform_faq_3_q'),
            answer: t('platform_faq_3_a')
        },
        {
            question: t('platform_faq_4_q'),
            answer: t('platform_faq_4_a')
        },
        {
            question: t('platform_faq_5_q'),
            answer: t('platform_faq_5_a')
        },
        {
            question: t('platform_faq_6_q'),
            answer: t('platform_faq_6_a')
        },
        {
            question: t('platform_faq_7_q'),
            answer: t('platform_faq_7_a')
        },
        {
            question: t('platform_faq_8_q'),
            answer: t('platform_faq_8_a')
        },
        {
            question: t('platform_faq_9_q'),
            answer: t('platform_faq_9_a')
        },
        {
            question: t('platform_faq_10_q'),
            answer: t('platform_faq_10_a')
        },
        {
            question: t('platform_faq_11_q'),
            answer: t('platform_faq_11_a')
        },
        {
            question: t('platform_faq_12_q'),
            answer: t('platform_faq_12_a')
        },
        {
            question: t('platform_faq_13_q'),
            answer: t('platform_faq_13_a')
        },
        {
            question: t('platform_faq_14_q'),
            answer: t('platform_faq_14_a')
        },
        {
            question: t('platform_faq_15_q'),
            answer: t('platform_faq_15_a')
        }
    ];

    const comparisons = [
        {
            title: t('comparison_visual_title'),
            chatgpt: t('chatgpt_text_heavy'),
            funblocks: t('funblocks_visual_friendly'),
            icon: FaImage
        },
        {
            title: t('comparison_exploration_title'),
            chatgpt: t('chatgpt_linear_chat'),
            funblocks: t('funblocks_multi_perspective'),
            icon: FaSitemap
        },
        {
            title: t('comparison_guidance_title'),
            chatgpt: t('chatgpt_passive_waiting'),
            funblocks: t('funblocks_proactive_guide'),
            icon: FaRocket
        },
        {
            title: t('comparison_learning_title'),
            chatgpt: t('chatgpt_answer_only'),
            funblocks: t('funblocks_thinking_process'),
            icon: FaBrain
        }
    ];

    return (
        <VStack width={'100%'} position="relative" overflowY="hidden" gap={'4px'}>
            <Header />

            <VStack overflowY="auto" height={`calc(100vh - 76px)`} alignItems={'center'} width="100%">
                {/* <Container
                maxW="container.xl"
                > */}
                <VStack
                    style={{ width: '100%', gap: '0px' }}
                >
                    {/* <Section gap='1.5rem'>
                        <Box textAlign="center">
                            <Heading
                                as={isMobile ? "h2" : "h1"}
                                size={isMobile ? "xl" : "2xl"}
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                mb={6}
                                mt={6}
                                lineHeight={1.2}
                            >
                                {t('platform_title')}
                            </Heading>
                            <Text fontSize={isMobile ? "lg" : "xl"} color="gray.600" maxW="2xl" mx="auto">
                                {t('platform_description')}
                            </Text>

                        </Box>

                    </Section> */}

                    <HeroSection />

                    {/* Tools Grid */}
                    <AIToolsSection isMobile={isMobile} basePath={basePath} />


                    {/* Add ThinkingSection here */}
                    <ThinkingSection />

                    {/* AIFlow Section */}
                    {/* Examples Showcase Section */}
                    <Section bg="gray.50">
                        <VStack spacing={8} align="center">
                            <Heading
                                size="xl"
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                textAlign="center"
                            >
                                {t('examples_showcase_title', 'See Our Tools in Action')}
                            </Heading>
                            <Text
                                fontSize="xl"
                                color="gray.600"
                                maxW="3xl"
                                textAlign="center"
                                mb={6}
                            >
                                {t('examples_showcase_desc', 'Explore real examples of what you can create with our AI-powered tools')}
                            </Text>
                            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} width="100%">
                                {[
                                    {
                                        title: t('example_mindmap_title', 'AI Mindmap Example'),
                                        description: t('example_mindmap_desc', 'Visualize complex topics with our AI Mindmap tool. This example shows a mindmap about the book "The Great Gatsby".'),
                                        imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindmap_book.png",
                                        link: "/mindmap"
                                    },
                                    {
                                        title: t('example_infographic_title', 'AI Infographic Example'),
                                        description: t('example_infographic_desc', 'Create beautiful infographics instantly. This example shows a witty and attractive infographic generated with InsightCards.'),
                                        imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_insightcards_paradoxical.png",
                                        link: "/infographic"
                                    },
                                    {
                                        title: t('example_slides_title', 'AI Slides Example'),
                                        description: t('example_slides_desc', 'Generate professional presentations in seconds. This example shows a competitor analysis presentation.'),
                                        imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aislides_beyond_chatgpt.png",
                                        link: "/slides"
                                    },
                                    {
                                        title: t('example_mindsnap_title', 'AI MindSnap Example'),
                                        description: t('example_mindsnap_desc', 'Transform topics into visual mental models. This example shows a SWOT analysis.'),
                                        imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_swot.png",
                                        link: "/mindsnap"
                                    }
                                ].map((example, index) => (
                                    <Box
                                        key={index}
                                        bg="white"
                                        borderRadius="xl"
                                        boxShadow="xl"
                                        overflow="hidden"
                                        transition="all 0.3s"
                                        _hover={{ transform: 'translateY(-5px)', boxShadow: '2xl' }}
                                    // as={Link}
                                    // target='_blank'
                                    // href={basePath + example.link}
                                    // textDecoration="none"
                                    >
                                        <Image
                                            src={example.imageSrc}
                                            alt={example.title}
                                            width="100%"
                                            height={isMobile ? "250px" : "400px"}
                                            objectFit="contain"
                                        />
                                        <Box p={6}>
                                            <Heading size="md" mb={2}>{example.title}</Heading>
                                            <Text color="gray.600" mb={4}>{example.description}</Text>
                                            <Button
                                                rightIcon={<FaChevronRight />}
                                                colorScheme="blue"
                                                variant="outline"
                                                size="sm"
                                                as={Link}
                                                target='_blank'
                                                href={basePath + example.link}
                                                textDecoration="none"
                                            >
                                                {t('example_try_tool', 'Try this tool')}
                                            </Button>
                                        </Box>
                                    </Box>
                                ))}
                            </SimpleGrid>
                        </VStack>
                    </Section>

                    <AIFlowSection />

                    {/* Features Section */}
                    {/* <Box textAlign="center" w="full" bg="gray.50" p={12} mt={4} pl={isMobile ? 4 : undefined} pr={isMobile ? 4 : undefined} borderRadius="xl"> */}
                    <Section bg='gray.50'>
                        <Heading mb={12} textAlign="center" size={'xl'} color={'#333'}>{t('why_funblocks')}</Heading>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                            {features.map((feature, index) => (
                                <Box
                                    key={index}
                                    bg="white"
                                    p={8}
                                    borderRadius="lg"
                                    boxShadow="md"
                                    _hover={{ transform: 'translateY(-4px)', boxShadow: 'lg' }}
                                    transition="all 0.3s"
                                >
                                    <Icon
                                        as={feature.icon}
                                        w={10}
                                        h={10}
                                        mb={4}
                                        style={{
                                            color: 'dodgerblue',
                                        }}
                                        bgGradient={feature.gradient}
                                        bgClip="text"
                                    />
                                    <Heading size="md" mb={4}>
                                        {feature.title}
                                    </Heading>
                                    <Text color="gray.600">
                                        {feature.description}
                                    </Text>
                                </Box>
                            ))}
                        </SimpleGrid>
                    </Section>


                    {/* Redesigned ChatGPT Comparison Section */}
                    <Section bg="white" py={{ base: 12, md: 16 }}>
                        <VStack spacing={{ base: 6, md: 8 }} width="100%">
                            {/* Section Header with Animation */}
                            <MotionBox
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.5 }}
                                textAlign="center"
                                maxW="3xl"
                                mx="auto"
                                px={{ base: 4, md: 0 }}
                            >
                                <Heading
                                    mb={{ base: 4, md: 5 }}
                                    color={'#333'}
                                    size={'xl'}
                                    bgGradient="linear(to-r, blue.500, purple.500)"
                                    bgClip="text"
                                >
                                    {t('why_not_chatgpt')}
                                </Heading>
                                <Text
                                    fontSize={{ base: "lg", md: "xl" }}
                                    color="gray.600"
                                    maxW="3xl"
                                    textAlign="center"
                                >
                                    {t('chatgpt_comparison_intro')}
                                </Text>
                            </MotionBox>

                            {/* Comparison Cards with Visual Contrast */}
                            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={{ base: 6, md: 8 }} width="100%" maxW="container.xl" mx="auto">
                                {comparisons.map((item, index) => (
                                    <MotionBox
                                        key={index}
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true }}
                                        transition={{ duration: 0.5, delay: index * 0.1 }}
                                        overflow="hidden"
                                        borderRadius="xl"
                                        boxShadow="lg"
                                        bg="white"
                                        border="1px solid"
                                        borderColor="gray.100"
                                    >
                                        {/* Card Header with Icon and Title */}
                                        <Flex
                                            direction="row"
                                            alignItems="center"
                                            bg="purple.50"
                                            p={{ base: 4, md: 5 }}
                                            borderBottom="1px solid"
                                            borderColor="gray.100"
                                            textAlign="center"
                                            gap={4}
                                        >
                                            <Icon
                                                as={item.icon}
                                                w={{ base: 8, md: 10 }}
                                                h={{ base: 8, md: 10 }}
                                                color="purple.500"
                                            />
                                            <Heading
                                                size={{ base: "sm", md: "md" }}
                                                color="purple.700"
                                                fontWeight="semibold"
                                            >
                                                {item.title}
                                            </Heading>
                                        </Flex>

                                        {/* Comparison Content */}
                                        <Box p={{ base: 5, md: 6 }}>
                                            {/* ChatGPT Section */}
                                            <Box
                                                mb={5}
                                                p={{ base: 4, md: 5 }}
                                                bg="gray.50"
                                                borderRadius="lg"
                                                borderLeft="4px solid"
                                                borderColor="gray.300"
                                            >
                                                <Flex align="center" mb={2}>
                                                    <Icon as={SiOpenai} color="gray.500" mr={2} />
                                                    <Text fontWeight="bold" color="gray.700">ChatGPT</Text>
                                                </Flex>
                                                <Text color="gray.600" fontSize={{ base: "sm", md: "md" }}>
                                                    {item.chatgpt}
                                                </Text>
                                            </Box>

                                            {/* FunBlocks Section */}
                                            <Box
                                                p={{ base: 4, md: 5 }}
                                                bg="blue.50"
                                                borderRadius="lg"
                                                borderLeft="4px solid"
                                                borderColor="blue.400"
                                            >
                                                <Flex align="center" mb={2}>
                                                    <Icon as={FaRocket} color="blue.500" mr={2} />
                                                    <Text fontWeight="bold" color="blue.600">FunBlocks AI</Text>
                                                </Flex>
                                                <Text color="blue.700" fontSize={{ base: "sm", md: "md" }}>
                                                    {item.funblocks}
                                                </Text>
                                            </Box>
                                        </Box>
                                    </MotionBox>
                                ))}
                            </SimpleGrid>

                            {/* Call to Action */}
                            <MotionBox
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.5, delay: 0.4 }}
                                mt={{ base: 6, md: 8 }}
                            >
                                <Button
                                    as={Link}
                                    href="#ai_tools"
                                    size={{ base: "md", md: "lg" }}
                                    colorScheme="blue"
                                    rightIcon={<FaArrowCircleRight />}
                                    _hover={{ transform: 'translateY(-2px)' }}
                                    transition="all 0.3s"
                                    px={{ base: 6, md: 8 }}
                                    py={{ base: 6, md: 7 }}
                                    borderRadius="full"
                                    boxShadow="md"
                                >
                                    {t('explore_tools')}
                                </Button>
                            </MotionBox>
                        </VStack>
                    </Section>

                    {/* Enhanced Comparison Section using ComparisonTable */}
                    <ComparisonTable
                        title={t('tools_comparison_title', 'How FunBlocks AI Compares')}
                        description={t('tools_comparison_description', 'See how FunBlocks AI stacks up against other AI tools and platforms in key areas that matter for productivity and learning.')}
                        highlightColumn="funblocks"
                        bg='gray.50'
                        columns={[
                            { key: "funblocks", label: "FunBlocks AI" },
                            { key: "chatgpt", label: "ChatGPT" },
                            { key: "other_tools", label: "Other AI Tools" }
                        ]}
                        features={[
                            {
                                name: t('comparison_feature_1', 'Visual Thinking Tools'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: false,
                                tooltip: t('comparison_feature_1_tooltip', 'Specialized tools for creating mindmaps, infographics, and visual presentations')
                            },
                            {
                                name: t('comparison_feature_2', 'Educational Frameworks'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: false,
                                tooltip: t('comparison_feature_2_tooltip', 'Built-in educational frameworks like Bloom\'s Taxonomy, Marzano, and SOLO')
                            },
                            {
                                name: t('comparison_feature_3', 'Multi-perspective Analysis'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: true,
                                tooltip: t('comparison_feature_3_tooltip', 'Ability to analyze topics from multiple perspectives and mental models')
                            },
                            {
                                name: t('comparison_feature_4', 'Structured Outputs'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: true,
                                tooltip: t('comparison_feature_4_tooltip', 'Consistently structured outputs optimized for learning and retention')
                            },
                            {
                                name: t('comparison_feature_5', 'Specialized for Learning'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: false,
                                tooltip: t('comparison_feature_5_tooltip', 'Tools specifically designed to enhance learning and comprehension')
                            },
                            {
                                name: t('comparison_feature_6', 'Visual Export Options'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: true,
                                tooltip: t('comparison_feature_6_tooltip', 'Export as SVG, PNG, PDF and other visual formats')
                            },
                            {
                                name: t('comparison_feature_7', 'Research-Backed Design'),
                                funblocks: true,
                                chatgpt: false,
                                other_tools: false,
                                tooltip: t('comparison_feature_7_tooltip', 'Tools designed based on cognitive science and learning research')
                            }
                        ]}
                    />

                    {/* LLM Support Section */}
                    <Section bg="white">
                        <VStack spacing={8} align="center">
                            <Heading
                                size="xl"
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                textAlign="center"
                                color={'#333'}
                            >
                                {t('llm_support_title')}
                            </Heading>
                            <Text
                                fontSize="xl"
                                color="gray.600"
                                maxW="3xl"
                                textAlign="center"
                                mb={6}
                            >
                                {t('llm_support_desc')}
                            </Text>

                            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={8} width="100%">
                                {[
                                    {
                                        name: t('model_openai'),
                                        description: t('model_openai_desc'),
                                        icon: SiOpenai,
                                        color: '#10a37f',
                                        gradient: 'linear(to-r, green.400, teal.500)',
                                        bgColor: 'rgba(16, 163, 127, 0.1)'
                                    },
                                    {
                                        name: t('model_anthropic'),
                                        description: t('model_anthropic_desc'),
                                        icon: SiAnthropic,
                                        color: '#b15dff',
                                        gradient: 'linear(to-r, purple.400, pink.500)',
                                        bgColor: 'rgba(177, 93, 255, 0.1)'
                                    },
                                    {
                                        name: t('model_google'),
                                        description: t('model_google_desc'),
                                        icon: SiGooglegemini,
                                        color: '#1a73e8',
                                        gradient: 'linear(to-r, blue.400, cyan.400)',
                                        bgColor: 'rgba(26, 115, 232, 0.1)'
                                    },
                                    {
                                        name: t('model_deepseek'),
                                        description: t('model_deepseek_desc'),
                                        // icon: DeepseekIcon,
                                        color: '#ff6b01',
                                        gradient: 'linear(to-r, orange.400, red.400)',
                                        bgColor: 'rgba(255, 107, 1, 0.1)'
                                    },
                                    // {
                                    //     name: t('model_mistral'),
                                    //     description: t('model_mistral_desc'),
                                    //     icon: SiMistral,
                                    //     color: '#007bff',
                                    //     gradient: 'linear(to-r, blue.500, purple.400)',
                                    //     bgColor: 'rgba(0, 123, 255, 0.1)'
                                    // },
                                    // {
                                    //     name: t('model_cohere'),
                                    //     description: t('model_cohere_desc'),
                                    //     icon: SiCohere,
                                    //     color: '#6d38e0',
                                    //     gradient: 'linear(to-r, purple.500, indigo.400)',
                                    //     bgColor: 'rgba(109, 56, 224, 0.1)'
                                    // }
                                ].map((model, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true }}
                                        transition={{
                                            duration: 0.5,
                                            delay: index * 0.1
                                        }}
                                    >
                                        <Box
                                            bg="white"
                                            p={6}
                                            borderRadius="xl"
                                            boxShadow="md"
                                            _hover={{
                                                transform: 'translateY(-4px)',
                                                boxShadow: 'xl',
                                                borderColor: model.color
                                            }}
                                            transition="all 0.3s"
                                            borderTop="4px solid"
                                            borderColor={model.color}
                                            position="relative"
                                            overflow="hidden"
                                            height="100%"
                                        >
                                            <Box
                                                position="absolute"
                                                top="0"
                                                left="0"
                                                right="0"
                                                height="100%"
                                                bg={model.bgColor}
                                                opacity="0.5"
                                                zIndex="0"
                                            />
                                            <VStack spacing={4} align="start" position="relative" zIndex="1" height="100%">
                                                <HStack>
                                                    {
                                                        model.icon &&
                                                        <Icon
                                                            as={model.icon}
                                                            w={10}
                                                            h={10}
                                                            color={model.color}
                                                        />
                                                    }

                                                    <Heading size="md" fontWeight="bold">
                                                        {model.name}
                                                    </Heading>
                                                </HStack>
                                                <Text color="gray.600">
                                                    {model.description}
                                                </Text>
                                                {/* <Spacer />
                                                    <Box
                                                        alignSelf="flex-end"
                                                        mt={2}
                                                    >
                                                        <Badge
                                                            colorScheme={index < 3 ? "green" : "blue"}
                                                            variant="subtle"
                                                            fontSize="sm"
                                                            px={2}
                                                            py={1}
                                                            borderRadius="full"
                                                        >
                                                            {index < 3 ? "Popular" : "Advanced"}
                                                        </Badge>
                                                    </Box> */}
                                            </VStack>
                                        </Box>
                                    </motion.div>
                                ))}
                            </SimpleGrid>

                            <Box
                                mt={8}
                                p={4}
                                borderRadius="lg"
                                bg="gray.100"
                                borderLeft="4px solid"
                                borderColor="blue.400"
                                maxW="3xl"
                            >
                                <Text
                                    fontSize="md"
                                    color="blue.500"
                                    textAlign="center"
                                >
                                    {t("llm_support_notes")}
                                </Text>
                            </Box>
                        </VStack>
                    </Section>

                    {/* Target Audience Section */}
                    <Section bg="blue.50">
                        <VStack spacing={8} align="center">
                            <Heading
                                size="xl"
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                textAlign="center"
                            >
                                {t('target_audience_title')}
                            </Heading>
                            <Text
                                fontSize="xl"
                                color="gray.600"
                                maxW="3xl"
                                textAlign="center"
                                mb={8}
                            >
                                {t('target_audience_desc')}
                            </Text>
                            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={8} width="100%">
                                {[
                                    {
                                        title: t('target_audience_students'),
                                        desc: t('target_audience_students_desc'),
                                        icon: FaUserGraduate
                                    },
                                    {
                                        title: t('target_audience_professionals'),
                                        desc: t('target_audience_professionals_desc'),
                                        icon: FaUserTie
                                    },
                                    {
                                        title: t('target_audience_creatives'),
                                        desc: t('target_audience_creatives_desc'),
                                        icon: FaPalette
                                    }
                                ].map((item, index) => (
                                    <Box
                                        key={index}
                                        bg="white"
                                        p={8}
                                        borderRadius="xl"
                                        boxShadow="xl"
                                        _hover={{ transform: 'translateY(-4px)', boxShadow: '2xl' }}
                                        transition="all 0.3s"
                                    >
                                        <VStack spacing={4} align="start">
                                            <Icon
                                                as={item.icon}
                                                w={10}
                                                h={10}
                                                color="blue.500"
                                            />
                                            <Heading size="md">{item.title}</Heading>
                                            <Text color="gray.600">{item.desc}</Text>
                                        </VStack>
                                    </Box>
                                ))}
                            </SimpleGrid>
                        </VStack>
                    </Section>

                    {/* Use Cases Section */}
                    <Section bg="gray.50">
                        <VStack spacing={8} align="center">
                            <Heading
                                size="xl"
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                textAlign="center"
                            >
                                {t('use_cases_title')}
                            </Heading>
                            <Text
                                fontSize="xl"
                                color="gray.600"
                                maxW="3xl"
                                textAlign="center"
                                mb={8}
                            >
                                {t('use_cases_desc')}
                            </Text>
                            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8} width="100%">
                                {[
                                    {
                                        title: t('use_case_1_title'),
                                        desc: t('use_case_1_desc'),
                                        icon: FaGraduationCap
                                    },
                                    {
                                        title: t('use_case_2_title'),
                                        desc: t('use_case_2_desc'),
                                        icon: FaChartLine
                                    },
                                    {
                                        title: t('use_case_3_title'),
                                        desc: t('use_case_3_desc'),
                                        icon: FaLightbulb
                                    },
                                    {
                                        title: t('use_case_4_title'),
                                        desc: t('use_case_4_desc'),
                                        icon: FaSearch
                                    },
                                    {
                                        title: t('use_case_5_title'),
                                        desc: t('use_case_5_desc'),
                                        icon: FaUserAlt
                                    },
                                    {
                                        title: t('use_case_6_title'),
                                        desc: t('use_case_6_desc'),
                                        icon: FaUsers
                                    }
                                ].map((item, index) => (
                                    <Box
                                        key={index}
                                        bg="white"
                                        p={8}
                                        borderRadius="xl"
                                        boxShadow="xl"
                                        _hover={{ transform: 'translateY(-4px)', boxShadow: '2xl' }}
                                        transition="all 0.3s"
                                    >
                                        <VStack spacing={4} align="start">
                                            <Icon
                                                as={item.icon}
                                                w={10}
                                                h={10}
                                                color="blue.500"
                                            />
                                            <Heading size="md">{item.title}</Heading>
                                            <Text color="gray.600">{item.desc}</Text>
                                        </VStack>
                                    </Box>
                                ))}
                            </SimpleGrid>
                        </VStack>
                    </Section>

                    {/* Case Studies Section */}
                    {
                        <CaseStudies
                            caseStudies={[
                                {
                                    icon: FaGraduationCap,
                                    title: t('case_study_1_title', 'Educational Institution Transformation'),
                                    industry: t('case_study_1_industry', 'Higher Education'),
                                    challenge: t('case_study_1_challenge', 'A leading university struggled with helping students visualize complex concepts across multiple disciplines, resulting in lower comprehension and retention rates.'),
                                    solution: t('case_study_1_solution', 'Implemented a comprehensive suite of FunBlocks AI tools across departments: MarzanoBrain and BloomBrain for creating structured learning materials, Slides for generating interactive presentations, and Brainstorming, Critical Thinking, and Creative Thinking tools to develop students\' cognitive abilities.'),
                                    results: t('case_study_1_results', 'Significant improvement in student comprehension and retention rates. Students demonstrated enhanced critical thinking skills and ability to connect concepts across disciplines. Faculty reported more engaging classroom discussions and higher quality student work.'),
                                    imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_infographics_process.png"
                                },
                                {
                                    icon: FaLightbulb,
                                    title: t('case_study_2_title', 'Innovation Thinking Enhancement'),
                                    industry: t('case_study_2_industry', 'Product Design & Marketing'),
                                    challenge: t('case_study_2_challenge', 'A multinational company struggled with fostering innovative thinking among their product design and marketing teams, resulting in predictable solutions and declining market differentiation.'),
                                    solution: t('case_study_2_solution', 'Integrated FunBlocks AI Brainstorming, MindKit, MindSnap, OKR Assistant, Task Planner and other FunBlocks AI tools into their ideation process. Teams used these tools to explore multiple perspectives, challenge assumptions, and visualize connections between seemingly unrelated concepts.'),
                                    results: t('case_study_2_results', 'Teams developed more innovative product designs and marketing campaigns that resonated with customers. The company reported increased creative output, more diverse solution sets, and improved cross-team collaboration on complex projects.'),
                                    imageSrc: "https://www.funblocks.net/img/portfolio/fullsize/aitools_mindsnap_eisenhower_matrix.png"
                                }
                            ]}
                            description={t('case_studies_description', 'See how organizations across different sectors have leveraged FunBlocks AI tools to solve real challenges and achieve measurable results.')}
                            appname="FunBlocks AI"
                            onClick={() => document.getElementById('ai_tools').scrollIntoView({ behavior: 'smooth' })}
                        />
                    }

                    {/* Research-Backed Section */}
                    <ResearchBacked
                        title={t('research_title', 'Research-Backed Approach')}
                        description={t('research_description', 'Our tools are built on solid scientific foundations and proven cognitive principles to maximize learning and productivity.')}
                        researchAreas={[
                            {
                                title: t('research_area_1_title', 'Cognitive Load Theory'),
                                description: t('research_area_1_description', 'Our visual tools reduce cognitive load by organizing information spatially, allowing users to process complex concepts more efficiently while minimizing mental effort.'),
                                icon: FaBrain,
                                color: 'blue',
                                citations: [{
                                    text: 'Sweller, J., van Merriënboer, J. J. G., & Paas, F. (2019). Cognitive Architecture and Instructional Design: 20 Years Later. Educational Psychology Review, 31(2), 261-292.',
                                    url: 'https://doi.org/10.1007/s10648-019-09465-5'
                                }, {
                                    text: 'Mayer, R. E. (2014). The Cambridge Handbook of Multimedia Learning (2nd ed.). Cambridge University Press.',
                                    url: 'https://doi.org/10.1017/CBO9781139547369'
                                }]
                            },
                            {
                                title: t('research_area_2_title', 'Visual Learning Efficacy'),
                                description: t('research_area_2_description', 'Research shows visual learning can improve understanding by up to 400% and retention by 38% compared to text-only learning, making complex information more accessible and memorable.'),
                                icon: FaImage,
                                color: 'green',
                                citations: [{
                                    text: 'Medina, J. (2014). Brain Rules: 12 Principles for Surviving and Thriving at Work, Home, and School (2nd ed.). Pear Press.',
                                    url: 'https://brainrules.net/brain-rules/'
                                }, {
                                    text: 'Paivio, A. (2014). Mind and Its Evolution: A Dual Coding Theoretical Approach. Psychology Press.',
                                    url: 'https://doi.org/10.4324/9781315785233'
                                }, {
                                    text: 'Mayer, R. E., & Moreno, R. (2003). Nine Ways to Reduce Cognitive Load in Multimedia Learning. Educational Psychologist, 38(1), 43-52.',
                                    url: 'https://doi.org/10.1207/S15326985EP3801_6'
                                }]
                            },
                            {
                                title: t('research_area_3_title', 'Mental Models & Frameworks'),
                                description: t('research_area_3_description', 'Our tools leverage established mental models and educational frameworks that help structure thinking, improve problem-solving capabilities, and enhance conceptual understanding across disciplines.'),
                                icon: MdInsights,
                                color: 'orange',
                                citations: [
                                    {
                                        text: 'Johnson-Laird, P. N. (2010). Mental models and human reasoning. Proceedings of the National Academy of Sciences, 107(43), 18243-18250.',
                                        url: 'https://doi.org/10.1073/pnas.1012933107'
                                    },
                                    {
                                        text: 'Gentner, D., & Stevens, A. L. (Eds.). (2014). Mental Models. Psychology Press.',
                                        url: 'https://doi.org/10.4324/9781315802725'
                                    },
                                    {
                                        text: 'Krathwohl, D. R. (2002). A Revision of Bloom\'s Taxonomy: An Overview. Theory Into Practice, 41(4), 212-218.',
                                        url: 'https://doi.org/10.1207/s15430421tip4104_2'
                                    }
                                ]
                            },
                            {
                                title: t('research_area_4_title', 'AI-Enhanced Learning'),
                                description: t('research_area_4_description', 'Studies show that AI-assisted learning tools can personalize the educational experience, provide adaptive feedback, and improve outcomes across diverse learning styles and contexts.'),
                                icon: FaRobot,
                                color: 'purple',
                                citations: [
                                    {
                                        text: 'UNESCO. (2021). AI and education: Guidance for policy-makers. UNESCO Digital Library.',
                                        url: 'https://unesdoc.unesco.org/ark:/48223/pf0000376709'
                                    },
                                    {
                                        text: 'Holmes, W., Bialik, M., & Fadel, C. (2019). Artificial Intelligence in Education: Promises and Implications for Teaching and Learning. Center for Curriculum Redesign.',
                                        url: 'https://curriculumredesign.org/our-work/artificial-intelligence-in-education/'
                                    },
                                    {
                                        text: 'Zawacki-Richter, O., Marín, V. I., Bond, M., & Gouverneur, F. (2019). Systematic review of research on artificial intelligence applications in higher education – where are the educators? International Journal of Educational Technology in Higher Education, 16, 39.',
                                        url: 'https://doi.org/10.1186/s41239-019-0171-0'
                                    }
                                ]
                            }
                        ]}
                        bg="blue.50"
                    />

                    {/* Testimonials Section - Using the reusable component */}
                    <Testimonials
                        appname="FunBlocks AI"
                        rating="4.8"
                        users="20,000+"
                        testimonials={[
                            {
                                content: t('testimonial_1_content', 'MindLadder completely transformed how I study for medical school. The way it breaks down complex topics into progressive layers helped me understand cardiovascular physiology in a way textbooks never could. I\'ve cut my study time by 30% while improving my grades!'),
                                author: 'Emily Johnson',
                                role: t('testimonial_1_role', 'Medical Student'),
                                organization: 'Stanford University',
                                rating: 5,
                                image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                            },
                            {
                                content: t('testimonial_2_content', 'As a product manager, I need to communicate complex ideas to different teams. The AI Infographic Generator has become my secret weapon for creating visually stunning concept maps and comparison charts in minutes instead of hours.'),
                                author: 'Michael Chen',
                                role: t('testimonial_2_role', 'Product Manager'),
                                organization: 'Salesforce',
                                rating: 5,
                                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                            },
                            {
                                content: t('testimonial_3_content', 'I\'ve tried many brainstorming tools, but FunBlocks AI Brainstorming Assistant is in a league of its own. It doesn\'t just generate ideas - it helps structure and refine them in ways I wouldn\'t have considered. It\'s like having a creative thinking partner available 24/7.'),
                                author: 'Sarah Williams',
                                role: t('testimonial_3_role', 'Creative Director'),
                                organization: 'Design Studio NYC',
                                rating: 4,
                                image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
                            }
                        ]}
                    />

                    {/* Enhanced FAQ Section with Categories */}
                    <Section bg="gray.50">
                        <VStack spacing={{ base: 6, md: 8 }} align="center" px={{ base: 3, md: 0 }}>
                            <Heading
                                size={{ base: "lg", md: "xl" }}
                                bgGradient="linear(to-r, blue.400, purple.500)"
                                bgClip="text"
                                textAlign="center"
                                lineHeight={{ base: "1.3", md: "1.2" }}
                            >
                                {t('common_questions')}
                            </Heading>
                            <Text
                                fontSize={{ base: "md", md: "xl" }}
                                color="gray.600"
                                maxW="3xl"
                                textAlign="center"
                                mb={{ base: 4, md: 6 }}
                                px={{ base: 2, md: 0 }}
                                lineHeight="1.6"
                            >
                                {t('faq_description', 'Find answers to commonly asked questions about FunBlocks AI tools and how they can help you.')}
                            </Text>

                            {/* FAQ Categories */}
                            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{ base: 6, md: 8 }} width="100%" maxW="5xl">
                                <Box width="100%">
                                    <Heading
                                        size={{ base: "sm", md: "md" }}
                                        mb={{ base: 3, md: 4 }}
                                        color="blue.600"
                                        px={{ base: 1, md: 0 }}
                                    >
                                        {t('faq_category_general', 'About FunBlocks AI')}
                                    </Heading>
                                    <Accordion allowMultiple>
                                        {/* Questions about what FunBlocks AI is, pricing, etc. */}
                                        {[faqs[0], faqs[1], faqs[2], faqs[8], faqs[9]].map((faq, index) => (
                                            <AccordionItem
                                                key={index}
                                                mb={{ base: 3, md: 4 }}
                                                border="none"
                                                bg="white"
                                                borderRadius="lg"
                                                boxShadow="md"
                                                _hover={{ boxShadow: 'lg' }}
                                                transition="all 0.3s"
                                            >
                                                <AccordionButton
                                                    pt={{ base: 3, md: 3 }}
                                                    pb={{ base: 3, md: 3 }}
                                                    px={{ base: 3, md: 4 }}
                                                    _hover={{ bg: 'transparent' }}
                                                    borderRadius="lg"
                                                    role="group"
                                                >
                                                    <Box flex="1" textAlign="left">
                                                        <Heading
                                                            size={{ base: "xs", md: "sm" }}
                                                            color="blue.600"
                                                            fontWeight="semibold"
                                                            lineHeight="1.4"
                                                            _groupHover={{ color: 'blue.700' }}
                                                        >
                                                            {faq.question}
                                                        </Heading>
                                                    </Box>
                                                    <AccordionIcon
                                                        color="blue.500"
                                                        _groupHover={{ transform: 'rotate(180deg)' }}
                                                        transition="transform 0.2s"
                                                    />
                                                </AccordionButton>
                                                <AccordionPanel
                                                    pb={{ base: 4, md: 6 }}
                                                    px={{ base: 3, md: 6 }}
                                                    pt={{ base: 1, md: 2 }}
                                                >
                                                    <Text
                                                        color="gray.600"
                                                        whiteSpace="pre-wrap"
                                                        fontSize={{ base: "sm", md: "md" }}
                                                        lineHeight="1.6"
                                                    >
                                                        {faq.answer}
                                                    </Text>
                                                </AccordionPanel>
                                            </AccordionItem>
                                        ))}
                                    </Accordion>
                                </Box>

                                <Box width="100%">
                                    <Heading
                                        size={{ base: "sm", md: "md" }}
                                        mb={{ base: 3, md: 4 }}
                                        color="purple.600"
                                        px={{ base: 1, md: 0 }}
                                    >
                                        {t('faq_category_features', 'Tools & Features')}
                                    </Heading>
                                    <Accordion allowMultiple>
                                        {/* Questions about specific tools and features */}
                                        {[faqs[3], faqs[4], faqs[5], faqs[6], faqs[7]].map((faq, index) => (
                                            <AccordionItem
                                                key={index}
                                                mb={{ base: 3, md: 4 }}
                                                border="none"
                                                bg="white"
                                                borderRadius="lg"
                                                boxShadow="md"
                                                _hover={{ boxShadow: 'lg' }}
                                                transition="all 0.3s"
                                            >
                                                <AccordionButton
                                                    pt={{ base: 3, md: 3 }}
                                                    pb={{ base: 3, md: 3 }}
                                                    px={{ base: 3, md: 4 }}
                                                    _hover={{ bg: 'transparent' }}
                                                    borderRadius="lg"
                                                    role="group"
                                                >
                                                    <Box flex="1" textAlign="left">
                                                        <Heading
                                                            size={{ base: "xs", md: "sm" }}
                                                            color="purple.600"
                                                            fontWeight="semibold"
                                                            lineHeight="1.4"
                                                            _groupHover={{ color: 'purple.700' }}
                                                        >
                                                            {faq.question}
                                                        </Heading>
                                                    </Box>
                                                    <AccordionIcon
                                                        color="purple.500"
                                                        _groupHover={{ transform: 'rotate(180deg)' }}
                                                        transition="transform 0.2s"
                                                    />
                                                </AccordionButton>
                                                <AccordionPanel
                                                    pb={{ base: 4, md: 6 }}
                                                    px={{ base: 3, md: 6 }}
                                                    pt={{ base: 1, md: 2 }}
                                                >
                                                    <Text
                                                        color="gray.600"
                                                        whiteSpace="pre-wrap"
                                                        fontSize={{ base: "sm", md: "md" }}
                                                        lineHeight="1.6"
                                                    >
                                                        {faq.answer}
                                                    </Text>
                                                </AccordionPanel>
                                            </AccordionItem>
                                        ))}
                                    </Accordion>
                                </Box>
                            </SimpleGrid>

                            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{ base: 6, md: 8 }} width="100%" maxW="5xl" mt={{ base: 4, md: 8 }}>
                                <Box width="100%">
                                    <Heading
                                        size={{ base: "sm", md: "md" }}
                                        mb={{ base: 3, md: 4 }}
                                        color="green.600"
                                        px={{ base: 1, md: 0 }}
                                    >
                                        {t('faq_category_usage', 'Use Cases & Applications')}
                                    </Heading>
                                    <Accordion allowMultiple>
                                        {/* Questions about how to use the tools in different contexts */}
                                        {[faqs[10], faqs[11], faqs[12], faqs[13], faqs[14]].map((faq, index) => (
                                            <AccordionItem
                                                key={index}
                                                mb={{ base: 3, md: 4 }}
                                                border="none"
                                                bg="white"
                                                borderRadius="lg"
                                                boxShadow="md"
                                                _hover={{ boxShadow: 'lg' }}
                                                transition="all 0.3s"
                                            >
                                                <AccordionButton
                                                    pt={{ base: 3, md: 3 }}
                                                    pb={{ base: 3, md: 3 }}
                                                    px={{ base: 3, md: 4 }}
                                                    _hover={{ bg: 'transparent' }}
                                                    borderRadius="lg"
                                                    role="group"
                                                >
                                                    <Box flex="1" textAlign="left">
                                                        <Heading
                                                            size={{ base: "xs", md: "sm" }}
                                                            color="green.600"
                                                            fontWeight="semibold"
                                                            lineHeight="1.4"
                                                            _groupHover={{ color: 'green.700' }}
                                                        >
                                                            {faq.question}
                                                        </Heading>
                                                    </Box>
                                                    <AccordionIcon
                                                        color="green.500"
                                                        _groupHover={{ transform: 'rotate(180deg)' }}
                                                        transition="transform 0.2s"
                                                    />
                                                </AccordionButton>
                                                <AccordionPanel
                                                    pb={{ base: 4, md: 6 }}
                                                    px={{ base: 3, md: 6 }}
                                                    pt={{ base: 1, md: 2 }}
                                                >
                                                    <Text
                                                        color="gray.600"
                                                        whiteSpace="pre-wrap"
                                                        fontSize={{ base: "sm", md: "md" }}
                                                        lineHeight="1.6"
                                                    >
                                                        {faq.answer}
                                                    </Text>
                                                </AccordionPanel>
                                            </AccordionItem>
                                        ))}
                                    </Accordion>
                                </Box>

                                <Box width="100%">
                                    <Heading
                                        size={{ base: "sm", md: "md" }}
                                        mb={{ base: 3, md: 4 }}
                                        color="orange.600"
                                        px={{ base: 1, md: 0 }}
                                    >
                                        {t('faq_category_technical', 'Technical & Support')}
                                    </Heading>
                                    <VStack
                                        spacing={{ base: 3, md: 4 }}
                                        align="start"
                                        bg="orange.50"
                                        p={{ base: 4, md: 6 }}
                                        borderRadius="lg"
                                        borderLeft="4px solid"
                                        borderColor="orange.400"
                                    >
                                        <Heading
                                            size={{ base: "xs", md: "sm" }}
                                            color="orange.600"
                                            lineHeight="1.4"
                                        >
                                            {t('faq_need_more_help', 'Need More Help?')}
                                        </Heading>
                                        <Text
                                            color="gray.600"
                                            fontSize={{ base: "sm", md: "md" }}
                                            lineHeight="1.6"
                                        >
                                            {t('faq_support_text', 'If you have questions not covered here, our support team is ready to help. You can also check our detailed documentation for more information.')}
                                        </Text>
                                        <Flex
                                            gap={{ base: 3, md: 4 }}
                                            flexWrap={{ base: "wrap", sm: "nowrap" }}
                                            width="100%"
                                        >
                                            <Button
                                                leftIcon={<FaQuestionCircle />}
                                                colorScheme="orange"
                                                variant="outline"
                                                size={{ base: "sm", md: "md" }}
                                                as={Link}
                                                href="https://discord.gg/XtdZFBy4uR"
                                                isExternal
                                                width={{ base: "100%", sm: "auto" }}
                                            >
                                                {t('contact_support', 'Contact Support')}
                                            </Button>
                                            <Button
                                                leftIcon={<FaBook />}
                                                colorScheme="blue"
                                                variant="outline"
                                                size={{ base: "sm", md: "md" }}
                                                as={Link}
                                                href="https://www.funblocks.net/aiflow"
                                                isExternal
                                                width={{ base: "100%", sm: "auto" }}
                                            >
                                                {t('view_documentation', 'View Documentation')}
                                            </Button>
                                        </Flex>
                                    </VStack>
                                </Box>
                            </SimpleGrid>
                        </VStack>
                    </Section>

                    <Footer />

                </VStack>

                {/* </Container> */}
            </VStack>

        </VStack>
    )
}

export default MainFrame