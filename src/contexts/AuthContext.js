import React, { createContext, useContext, useEffect, useState } from 'react';
import { apiUrl } from '../utils/apiUtils';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [user, setUser] = useState(null);

  const openLoginModal = () => {
    setIsLoginModalOpen(true);
  };
  const closeLoginModal = () => setIsLoginModalOpen(false);

  const checkUserInfo = async () => {
    try {
      const response = await fetch(`${apiUrl}/users/info`, {
        method: 'GET',
        credentials: 'include', // 确保带上 cookie/session
      });

      if (response.ok) {
        const result = await response.json();

        setUser(result.data);
        setIsLoggedIn(true);

        // 同步到 localStorage，方便下次快速恢复
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('user', JSON.stringify(result.data));

        // 如果登录成功且 modal 是打开的，关闭 modal
        if (isLoginModalOpen) {
          setIsLoginModalOpen(false);
        }

        return true;
      } else if (response.status === 403) {
        setIsLoggedIn(false);
        setUser(null);

        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('user');

        // onLoginRequired?.();
        return false;
      } else {
        throw new Error('Failed to fetch user info');
      }
    } catch (error) {
      console.error('Error fetching user info:', error);
      setIsLoggedIn(false);
      setUser(null);
      return false;
    }
  };

  useEffect(() => {
    checkUserInfo();
  }, [apiUrl]);

  const logon = (user) => {
    localStorage.setItem('isLoggedIn', 'true');
    localStorage.setItem('user', JSON.stringify(user));
    setUser(user)
    setIsLoggedIn(true);
    setIsLoginModalOpen(false);
  };

  const logout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('user');
    setUser(null);
    setIsLoggedIn(false);
  };

  return (
    <AuthContext.Provider value={{
      isLoggedIn,
      logon,
      logout,
      isLoginModalOpen,
      openLoginModal,
      closeLoginModal,
      user,
      checkUserInfo,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);