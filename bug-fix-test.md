# Image Editor Bug Fix 测试

## 问题描述
当用户在image-editor中：
1. 选择图片后，图片预览正常显示
2. 继续输入编辑指令时，图片预览消失
3. 点击"编辑图片"按钮时，弹出错误提示要求选择图片

## 根本原因
在 `CardGenerator.js` 中的 `handleInputChange` 函数直接替换了整个 `inputData` 状态：

```javascript
// 问题代码
const handleInputChange = (data) => {
    setInputData(data);  // 这会覆盖之前的 imageUrl
};
```

当用户输入文本时，`InputForm` 的 `handleChange` 函数只传递 `{ message: newValue }`，导致之前的 `imageUrl` 被覆盖。

## 修复方案
修改所有 `setInputData` 调用来合并状态而不是替换：

### 1. 修复 `handleInputChange` 函数
```javascript
// 修复后的代码
const handleInputChange = (data) => {
    setInputData(prevData => ({
        ...prevData,
        ...data
    }));
};
```

### 2. 修复 `handleExampleSelect` 函数
```javascript
// 修复后的代码
const handleExampleSelect = (type, message) => {
    setCardType(selectableCardTypes.find(t => t.value === type));
    setInputData(prevData => ({ ...prevData, message }));
};
```

### 3. 修复成功后的状态重置
```javascript
// 修复后的代码
!data.err && app !== APP_TYPE.horoscope && setInputData(prevData => ({
    ...prevData,
    message: '',
    url: ''
}));
```

## 测试步骤

### 1. 基本功能测试
1. 访问 `/aitools/image-editor` 页面
2. 点击"选择图片"按钮，上传一张图片
3. 验证图片预览正常显示
4. 在编辑指令框中输入文本（如："让图片更亮"）
5. 验证图片预览仍然显示
6. 点击"编辑图片"按钮
7. 验证不会出现"请选择图片"的错误提示

### 2. URL输入测试
1. 在图片URL输入框中粘贴一个有效的图片链接
2. 验证图片预览正常显示
3. 在编辑指令框中输入文本
4. 验证图片预览仍然显示
5. 点击"编辑图片"按钮
6. 验证功能正常

### 3. 状态切换测试
1. 先上传图片A
2. 输入编辑指令
3. 再上传图片B（替换图片A）
4. 验证图片预览更新为图片B
5. 修改编辑指令
6. 验证图片预览仍然是图片B

## 预期结果
- ✅ 图片上传后预览正常显示
- ✅ 输入编辑指令时图片预览保持显示
- ✅ 点击编辑按钮时不会出现"请选择图片"错误
- ✅ 图片URL和文件上传都正常工作
- ✅ 状态管理正确，不会丢失数据

## 相关文件
- `src/components/CardGenerator/CardGenerator.js` - 主要修复
- `src/components/CardGenerator/InputForm.js` - 图片处理逻辑
